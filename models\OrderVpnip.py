from sqlalchemy import Column, String, Integer, Float, DateTime, Boolean, select, text, func, and_, Numeric, Text, Index
from core.database import Base, db
import logging

logger = logging.getLogger(__name__)

class OrderVpnip(Base):
    __tablename__ = "dbv_ebis_v_amdes_nf_aktif_vpnip_final"
    __table_args__ = (
        Index('idx_vpnip_periode', 'periode'),
        Index('idx_vpnip_periode_service', 'periode', 'service_type'),
        Index('idx_vpnip_divisi', 'divisi'),
        {'schema': 'satgas_ai'}
    )

    __additional_tablename__ = "dbv_wins_v_amdes_nf_aktif_vpnip_final"

    periode = Column(Text, primary_key=True)
    customer_ref = Column(Text)
    account_num = Column(Text)
    nip_nas = Column(Text)
    custaccntname = Column(Text)
    account_name = Column(Text)
    business_share = Column(Text)
    divisi = Column(Text)
    top_cc_dgs = Column(Text)
    product_label = Column(Text)
    cust_order_num = Column(Text)
    currency_code = Column(Text)
    install = Column(Numeric(38, 20))
    abonemen = Column(Numeric(38, 20))
    circuit_id = Column(Text)
    circuit_name = Column(Text)
    circuit_speed = Column(Text)
    bandwidth_a = Column(Numeric(38, 20))
    bandwidth_b = Column(Numeric(38, 20))
    product_name = Column(Text)
    product_group = Column(Text)
    product_seq = Column(Numeric(38, 20))
    status_reason_txt = Column(Text)
    effective_dtm = Column(Text)
    tariff_name = Column(Text)
    ppn = Column(Text)
    tgl_suspend = Column(Text)
    suspend = Column(Text)
    update_dat = Column(Text)
    billcomdate_1 = Column(Text)
    service_type = Column(Text)
    qos = Column(Text)
    qos_normalized = Column(Text)
    best_effort = Column(Numeric(38, 20))
    critical = Column(Numeric(38, 20))
    interactive = Column(Numeric(38, 20))
    revenue = Column(Numeric(38, 20))
    tarif_best_effort = Column(Numeric(38, 20))
    tarif_critical = Column(Numeric(38, 20))
    tarif_interactive = Column(Numeric(38, 20))
    tarif_total = Column(Numeric(38, 20))
    hjm = Column(Numeric(38, 20))
    diskon_rp = Column(Numeric(38, 20))
    diskon_percent = Column(Numeric(38, 20))
    margin_rev_hjm_rp = Column(Numeric(38, 20))
    margin_rev_hjm_percent = Column(Numeric(38, 20))
    kat_hjm = Column(Text)
    sto_code = Column(Text)
    sto = Column(Text)
    service_witel = Column(Text)
    zona_tarif_new = Column(Text)
    agree_name = Column(Text)
    agree_start_date = Column(Text)
    agree_end_date = Column(Text)
    li_status_date = Column(Text)
    order_subtype = Column(Text)
    cust_cndc = Column(Text)
    sto_name_cndc = Column(Text)
    pull_thru_cndc = Column(Text)
    tarif_bw_performance = Column(Numeric(38, 20))
    uim_access_device = Column(Text)
    uim_uplinkport = Column(Text)
    uim_bandwidth = Column(Text)
    uim_vlan = Column(Text)
    uim_vrf = Column(Text)
    uim_vcid = Column(Text)
    uim_metro_access_device = Column(Text)
    uim_metro_service_device = Column(Text)
    uim_nte_device = Column(Text)
    uim_pe_router_device = Column(Text)
    uim_in_bw = Column(Text)
    uim_out_bw = Column(Text)
    uim_pe_subinterface = Column(Text)
    uim_pe_type_subinterface = Column(Text)
    uim_access_technology = Column(Text)
    uim_routing = Column(Text)
    uim_latitude = Column(Text)
    uim_longitude = Column(Text)
    uim_service_point = Column(Text)
    uim_target = Column(Text)
    service_type_1 = Column(Text)
    network_id = Column(Text)
    nip_nas_cbase = Column(Text)
    standard_name_cbase = Column(Text)
    segmen_cbase = Column(Text)
    bill_mny = Column(Numeric(38, 20))
    cx_termin_value = Column(Numeric(38, 20))
    cx_termin_flg = Column(Text)
    termin_seq_flg = Column(Text)
    cx_otc_amount = Column(Numeric(38, 20))
    termin_hjm = Column(Numeric(38, 20))
    termin_tarif = Column(Numeric(38, 20))
    cntrc_duration = Column(Numeric(38, 20))
    jml_sequence = Column(Numeric(38, 20))
    revenue_final = Column(Numeric(38, 20))
    hjm_final = Column(Numeric(38, 20))
    tarif_final = Column(Numeric(38, 20))
    is_ao_period = Column(Text)
    flag_otc = Column(Text)
    otc_seq_flg = Column(Text)
    revenue_otc = Column(Numeric(38, 20))
    tarif_otc = Column(Numeric(38, 20))
    hjm_otc = Column(Numeric(38, 20))
    term_payment = Column(Text)
    revenue_mrc = Column(Numeric(38, 20))
    tarif_mrc = Column(Numeric(38, 20))
    hjm_mrc = Column(Numeric(38, 20))
    mrc_seq_flg = Column(Text)
    flag_top = Column(Text)
    reg_new = Column(Text)
    ca_id = Column(Text)
    ba_id = Column(Text)
    sa_id = Column(Text)
    ca_divisi = Column(Text)
    ba_divisi = Column(Text)
    sa_divisi = Column(Text)
    ca_segmen = Column(Text)
    ba_segmen = Column(Text)
    sa_segmen = Column(Text)
    witel_new = Column(Text)
    value_speed_mbps = Column(Text)

    @classmethod
    async def get_data_with_fallback(cls, session, query, year: int):
        """Scan data dari kedua tabel dan gabungkan hasilnya"""
        try:
            all_data = []
            
            # Scan dari tabel utama (ebis)
            logger.info(f"Scanning data dari {cls.__tablename__}")
            result = await session.execute(query)
            ebis_data = result.all()
            if ebis_data:
                logger.info(f"Ditemukan {len(ebis_data)} data dari {cls.__tablename__}")
                all_data.extend(ebis_data)
            
            # Scan dari tabel tambahan (wins)
            logger.info(f"Scanning data dari {cls.__additional_tablename__}")
            
            # Ambil query string dan parameter dari query asli
            compiled_query = query.compile(compile_kwargs={"literal_binds": True})
            query_str = str(compiled_query)
            params = compiled_query.params
            
            # Ganti nama tabel dalam query string
            additional_query_str = query_str.replace(
                f"{cls.__table_args__[3]['schema']}.{cls.__tablename__}", 
                f"{cls.__table_args__[3]['schema']}.{cls.__additional_tablename__}"
            )
            
            # Buat query baru menggunakan text()
            additional_query = text(additional_query_str)
            
            # Jalankan query dengan parameter yang sama
            result = await session.execute(additional_query, params)
            wins_data = result.all()
            if wins_data:
                logger.info(f"Ditemukan {len(wins_data)} data dari {cls.__additional_tablename__}")
                all_data.extend(wins_data)
            
            # Log total data yang ditemukan
            total_data = len(all_data)
            if total_data > 0:
                logger.info(f"Total data yang ditemukan dari kedua tabel: {total_data}")
            else:
                logger.warning("Tidak ada data yang ditemukan dari kedua tabel")
            
            return all_data
            
        except Exception as e:
            logger.error(f"Error dalam get_data_with_fallback: {str(e)}", exc_info=True)
            raise 