import logging
import os
from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker, AsyncSession
from sqlalchemy.orm import declarative_base
from sqlalchemy.exc import SQLAlchemyError
if os.environ.get("ENVIRONTMENT") != "prod":
    from dotenv import load_dotenv
 
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
from settings import (
    DB_USER,
    DB_PASS,
    DB_HOST,
    DB_PORT,
    DB_NAME,
)
DB_USER = DB_USER
DB_PASS = DB_PASS
DB_HOST = DB_HOST
DB_PORT = DB_PORT
DB_NAME = DB_NAME

DATABASE_URL = f"postgresql+asyncpg://{DB_USER}:{DB_PASS}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

engine = create_async_engine(
    DATABASE_URL,
    pool_size=30,
    max_overflow=40,
    pool_recycle=1800,
    pool_timeout=30,
    echo=False,
)

async_session = async_sessionmaker(
    bind=engine,
    expire_on_commit=False,
    autoflush=False,
    autocommit=False,
    class_=AsyncSession,
)

async def get_db():
    async with async_session() as session:
        try:
            yield session
        except SQLAlchemyError as e:
            logger.error(f"Database error: {e}")
            await session.rollback()
            raise
        finally:
            await session.close()


Base = declarative_base()
