import asyncio
from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import sentry_sdk
from contextlib import asynccontextmanager
from fastapi_utilities import repeat_at
from settings import (
    CORS_ALLOWED_ORIGINS,
    SENTRY_DSN,
    SENTRY_TRACES_SAMPLE_RATES,
    FILE_STORAGE_ADAPTER,
    ENVIRONTMENT
)
from core.logging_config import logger
from core.database import db
from routes.scheduler import router as scheduler_router  
from fastapi.responses import HTMLResponse
import os


if SENTRY_DSN != None:  # NOQA
    sentry_sdk.init(
        dsn=SENTRY_DSN,
        # Set traces_sample_rate to 1.0 to capture 100%
        # of transactions for performance monitoring.
        # We recommend adjusting this value in production,
        traces_sample_rate=SENTRY_TRACES_SAMPLE_RATES,
    )


# TARUH CONJOB DISINI YA BANG SEMUANYAA BIG HUGG
# @asynccontextmanager
# async def lifespan(app: FastAPI):
    # --- startup ---
    # Uncomment untuk men<PERSON><PERSON>an scheduled_task saat startup
    # await run_scheduled_task("call_monthly_summary")
    # yield
    # --- shutdown ---


# Inisialisasi FastAPI berdasarkan ENVIRONTMENT
fastapi_kwargs = {
    "title": "Telkom AI Machine Learning Service",
    "swagger_ui_oauth2_redirect_url": "/docs/oauth2-redirect",
    "swagger_ui_init_oauth": {
        "clientId": "your-client-id",
        "authorizationUrl": "/auth/token",
        "tokenUrl": "/auth/token",
    },
    # "lifespan": lifespan,  
}
if ENVIRONTMENT == 'dev':
    fastapi_kwargs.update({
        "docs_url": "/docs",
        "redoc_url": None,
        "openapi_url": "/openapi.json",
    })
elif ENVIRONTMENT == 'prod':
    fastapi_kwargs.update({
        "docs_url": None,
        "redoc_url": None,
        "openapi_url": None,
    })
app = FastAPI(**fastapi_kwargs)

# @repeat_at(cron="1 0 1 * *")
# async def scheduled_task():
#     """
#     Scheduled task yang berjalan setiap bulan pada tanggal 1 jam 01:00
#     untuk menjalankan analisis ringkasan Astinet dan IP Transit per bulan
#     """
#     logger.info("Menjalankan analisis bulanan terjadwal")
#     try:
#         # Gunakan asyncio.create_task untuk menjalankan async function dengan proper
#         loop = asyncio.new_event_loop()
#         asyncio.set_event_loop(loop)
#         try:
#             await run_scheduled_task("call_monthly_summary")
#             logger.info("Analisis bulanan selesai dengan sukses")
#             print("✅ Analisis bulanan selesai dengan sukses!")
#         finally:
#             loop.close()
#     except Exception as e:
#         logger.error(f"Error dalam analisis bulanan terjadwal: {str(e)}")
#         print(f"❌ Error dalam analisis bulanan: {str(e)}")
#         sentry_sdk.capture_exception(e)


app.add_middleware(
    CORSMiddleware,
    allow_origins=CORS_ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["POST", "GET"],
    allow_headers=["*"],
)
if FILE_STORAGE_ADAPTER != 'minio':
    app.mount("/static", StaticFiles(directory="static"))



# app.include_router(auth_router, prefix="/auth")
app.include_router(scheduler_router, prefix="/scheduler")  
# app.include_router(database_router)



@app.get("/")
async def hello():
    html_content = """
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <title>Telkom AI Machine Learning Service</title>
        <style>
            body {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: #fff;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 100vh;
                margin: 0;
            }
            .container {
                background: rgba(0,0,0,0.3);
                padding: 40px 60px;
                border-radius: 16px;
                box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
                text-align: center;
            }
            h1 {
                margin-bottom: 10px;
                font-size: 2.5rem;
                letter-spacing: 2px;
            }
            p {
                font-size: 1.2rem;
                margin-top: 0;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>👋 Welcome to Our Machine Learning Service</h1>
            <p>Happy Code</p>
            <p>Bismillah bro</p>
        </div>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

DB_USER = os.environ.get('DB_USER')
DB_PASS = os.environ.get('DB_PASS')
DB_HOST = os.environ.get('DB_HOST')
DB_PORT = os.environ.get('DB_PORT')
DB_NAME = os.environ.get('DB_NAME')


