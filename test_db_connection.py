import asyncio
from sqlalchemy import text
from core.database import db
import signal
from contextlib import asynccontextmanager
from asyncio import TimeoutError as AsyncTimeoutError
import logging

# Konfigurasi logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Timeout handler
class TimeoutError(Exception):
    pass

@asynccontextmanager
async def timeout(seconds):
    """Timeout context manager yang kompatibel dengan ProactorEventLoop"""
    try:
        async with asyncio.timeout(seconds):
            yield
    except AsyncTimeoutError:
        raise TimeoutError(f"Operation timed out after {seconds} seconds")

async def test_connection():
    """Test koneksi database dan akses tabel secara sederhana"""
    try:
        print("🔍 Testing database connection...")
        
        async with timeout(10):  # 10 detik timeout
            # Test 1: Koneksi dasar ke database
            async with db.async_session() as session:
                query = text("SELECT current_database(), current_user;")
                result = await session.execute(query)
                db_info = result.fetchone()
                print("✅ Koneksi database berhasil!")
                print(f"Database: {db_info[0]}")
                print(f"User: {db_info[1]}")
            
        return True
        
    except TimeoutError as te:
        print(f"❌ Timeout: {te}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        print(f"Type: {type(e).__name__}")
        return False

def test_connection_old():
    try:
        # Mencoba melakukan koneksi ke database
        db.connect()
        logger.info("Berhasil terhubung ke database!")
        
        # Menutup koneksi
        db.close()
        logger.info("Koneksi database ditutup.")
        
    except Exception as e:
        logger.error(f"Gagal terhubung ke database: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_connection())
