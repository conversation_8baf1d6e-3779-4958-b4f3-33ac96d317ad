"""
Script untuk testing scheduler dan scheduled task
Gunakan script ini untuk memverifikasi bahwa scheduler dapat ber<PERSON>lan dengan baik
"""
import asyncio
import sys
import os
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from core.myworker import run_scheduled_task
from core.logging_config import logger

async def test_scheduled_task():
    """
    Function untuk test manual scheduled task
    """
    print("🧪 Testing Scheduled Task")
    print("=" * 50)
    print(f"⏰ Test dimulai pada: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        logger.info("Starting manual test of scheduled task")
        
        # Test run scheduled task
        await run_scheduled_task(func_name="call_astinet_summary_permonth")
        
        print("✅ Test scheduled task berhasil!")
        logger.info("Manual test of scheduled task completed successfully")
        
    except Exception as e:
        print(f"❌ Error dalam test scheduled task: {str(e)}")
        logger.error(f"Error in manual test of scheduled task: {str(e)}")
        raise

def test_cron_expression():
    """
    Function untuk memverifikasi cron expression
    """
    print("🕒 Testing Cron Expression")
    print("=" * 50)
    
    cron_expression = "0 1 1 * *"
    print(f"Cron Expression: {cron_expression}")
    print("Arti: Setiap tanggal 1 setiap bulan pada jam 01:00")
    print()
    print("Format cron: minute hour day-of-month month day-of-week")
    print("0 = menit ke-0 (jam xx:00)")
    print("1 = jam ke-1 (01:00)")
    print("1 = tanggal 1")
    print("* = setiap bulan")
    print("* = setiap hari dalam seminggu")
    print()
    print("✅ Cron expression valid untuk monthly task")

def show_scheduler_info():
    """
    Function untuk menampilkan informasi tentang scheduler
    """
    print("📋 Informasi Scheduler")
    print("=" * 50)
    print("Scheduler menggunakan: fastapi-utilities repeat_at decorator")
    print("Fungsi yang dijadwalkan: call_astinet_summary_permonth")
    print("Jadwal: Setiap tanggal 1 setiap bulan jam 01:00")
    print("Environment: Development/Production")
    print()
    print("📌 Untuk mengaktifkan scheduler:")
    print("1. Pastikan fastapi-utilities terinstall (✅ sudah ada di requirements.txt)")
    print("2. Uncomment lifespan handler di fastapi_kwargs jika ingin auto-start")
    print("3. Scheduler akan berjalan otomatis sesuai cron schedule")
    print()
    print("🔧 Untuk testing manual:")
    print("   python test_scheduler.py")

async def main():
    """
    Main function untuk menjalankan semua test
    """
    print("🚀 Scheduler Testing Suite")
    print("=" * 60)
    print()
    
    # Test 1: Informasi scheduler
    show_scheduler_info()
    print()
    
    # Test 2: Validasi cron expression
    test_cron_expression()
    print()
    
    # Test 3: Manual test scheduled task
    await test_scheduled_task()
    print()
    
    print("🎉 Semua test selesai!")
    print("=" * 60)

if __name__ == "__main__":
    # Jalankan test
    asyncio.run(main())
