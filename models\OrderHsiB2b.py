from sqlalchemy import Column, String, Integer, Float, DateTime, Boolean, select, text, func, and_, Numeric, Index
from core.database import Base, db
import logging

logger = logging.getLogger(__name__)

class OrderHsiB2b(Base):
    __tablename__ = "dbv_ebis_hsi_b2b_profile_new"
    __table_args__ = (
        Index('idx_hsi_b2b_period', 'period'),
        {'schema': 'satgas_ai'}
    )

    citem = Column(String)
    hjm = Column(String)
    regional = Column(String)
    kw_inet = Column(String)
    lgest = Column(String)
    notel = Column(String)
    period = Column(DateTime, primary_key=True)  # Ubah dari String ke DateTime
    tarif = Column(String)
    trems_rev = Column(Numeric(38, 20))
    witel = Column(String)
    servicemapping = Column(String)
    servicegroup = Column(String)
    prodtype = Column(String)
    pack_name = Column(String)
    inet_basic = Column(String)

    # @classmethod
    # async def get_data_with_fallback(cls, session, query, year: int):
    #     """Scan data dari tabel utama saja karena tidak ada fallback"""
    #     try:
    #         logger.info(f"Scanning data dari {cls.__tablename__}")
    #         result = await session.execute(query)
    #         data = result.all()
    #         if data:
    #             logger.info(f"Ditemukan {len(data)} data dari {cls.__tablename__}")
    #         else:
    #             logger.warning("Tidak ada data yang ditemukan")
    #         return data
            
    #     except Exception as e:
    #         logger.error(f"Error dalam get_data_with_fallback: {str(e)}", exc_info=True)
    #         raise 