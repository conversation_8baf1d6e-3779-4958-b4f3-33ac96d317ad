import pandas as pd
from typing import Optional, List
from sqlalchemy import select, func, or_, and_, extract, cast, Numeric
from sqlalchemy.dialects import postgresql
from core.database import db
from ml.generate_monthly_summary_schedule import generate_monthly_summary_schedule
from models.product_config import get_product_config, get_all_product_names
import logging
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta 

logger = logging.getLogger(__name__)

class SchedulerRepository:
    @staticmethod
    def _get_model_config(model_class):
        """Get basic configuration for the model based on its structure"""
        # Try to get config from product_config first
        model_name = model_class.__name__.lower().replace('order', '')
        
        # Fix mapping untuk nama yang tidak sesuai
        model_mapping = {
            'hsib2b': 'hsi_b2b',
            'astinet': 'astinet',
            'sap': 'sap',
            "voicepots": "voice_pots",
            'churndatacomm': 'churn_datacomm',
            'globallink': 'global_link',
            'neucentrixabonemen': 'neucentrix_abonemen',
            'neucentrixrevenue': 'neucentrix_revenue',
            'sldigital': 'sl_domestik',
            'sdwanconnectivity': 'sdwan_connectivity',
            'sdwanservice': 'sdwan_service',
            'churninternetiptransit': 'churn_internet_iptransit',
            'churninternetastinet': 'churn_internet_astinet',
            'vpnip': 'vpn_ip',
            'metroe': 'metro_ethernet'
        }
        
        # Gunakan mapping jika ada
        if model_name in model_mapping:
            mapped_name = model_mapping[model_name]
            logger.info(f"Mapping '{model_name}' → '{mapped_name}'")
            model_name = mapped_name
        
        logger.info(f"Looking for product config with name: '{model_name}'")
        product_config = get_product_config(model_name)
        
        if product_config:
            logger.info(f"✅ Using product config for {model_name}: {product_config}")
            # Map product_config keys ke format yang diharapkan repository
            config = {}
            
            # Map period column
            if 'date_column' in product_config:
                config['period_column_name'] = product_config['date_column']
                logger.info(f"  → period_column_name: {product_config['date_column']}")
            else:
                config['period_column_name'] = 'periode'
                logger.warning(f"  → No date_column found, using default: periode")
            
            # Map period type
            if 'period_type' in product_config:
                config['period_column_type'] = product_config['period_type']
                logger.info(f"  → period_column_type: {product_config['period_type']}")
            else:
                config['period_column_type'] = 'integer_yyyymm'
                logger.warning(f"  → No period_type found, using default: integer_yyyymm")
            
            # Map service type column
            if 'service_column' in product_config:
                config['service_type_column_name'] = product_config['service_column']
                logger.info(f"  → service_type_column_name: {product_config['service_column']}")
            else:
                config['service_type_column_name'] = 'service_type'
                logger.warning(f"  → No service_column found, using default: service_type")
            
            # Map target column
            if 'revenue_column' in product_config:
                config['target_column_name'] = product_config['revenue_column']
                logger.info(f"  → target_column_name: {product_config['revenue_column']}")
            else:
                config['target_column_name'] = 'revenue'
                logger.warning(f"  → No revenue_column found, using default: revenue")
            
            # Map division column
            config['division_column_name'] = 'divisi'
            
            # Basic selected features
            config['selected_features'] = ['divisi', 'segmen', 'witel']
            
            logger.info(f"✅ Final config: {config}")
            return config
        
        # Fallback to dynamic detection
        logger.info(f"Using dynamic config detection for {model_class.__name__}")
        config = {}
        
        # Determine period column and type
        if hasattr(model_class, 'periode_rev'):
            config['period_column_name'] = 'periode_rev'
            config['period_column_type'] = 'datetime'
        elif hasattr(model_class, 'periode'):
            config['period_column_name'] = 'periode'
            config['period_column_type'] = 'integer_yyyymm'
        elif hasattr(model_class, 'period_date'):
            config['period_column_name'] = 'period_date'
            config['period_column_type'] = 'datetime'
        elif hasattr(model_class, 'period'):
            config['period_column_name'] = 'period'
            config['period_column_type'] = 'integer_yyyymm'
        elif hasattr(model_class, 'periode_nf_aktif'):
            config['period_column_name'] = 'periode_nf_aktif'
            config['period_column_type'] = 'integer_yyyymm'
        else:
            config['period_column_name'] = 'periode'
            config['period_column_type'] = 'integer_yyyymm'
        
        # Determine service type column
        if hasattr(model_class, 'gl_dss_prod_cat'):
            config['service_type_column_name'] = 'gl_dss_prod_cat'
        elif hasattr(model_class, 'service_type'):
            config['service_type_column_name'] = 'service_type'
        elif hasattr(model_class, 'service_type_1'):
            config['service_type_column_name'] = 'service_type_1'
        elif hasattr(model_class, 'variant'):
            config['service_type_column_name'] = 'variant'
        elif hasattr(model_class, 'edge_type'):
            config['service_type_column_name'] = 'edge_type'
        elif hasattr(model_class, 'product_name'):
            config['service_type_column_name'] = 'product_name'
        elif hasattr(model_class, 'name'):
            config['service_type_column_name'] = 'name'
        elif hasattr(model_class, 'produk'):
            config['service_type_column_name'] = 'produk'
        elif hasattr(model_class, 'product_group'):  
            config['service_type_column_name'] = 'product_group'
        elif hasattr(model_class, 'produk_group'):
            config['service_type_column_name'] = 'produk_group'
        elif hasattr(model_class, 'produk_label'):
            config['service_type_column_name'] = 'produk_label'
        else:
            config['service_type_column_name'] = 'service_type'
        
        # Determine division column
        if hasattr(model_class, 'divisi'):
            config['division_column_name'] = 'divisi'
        else:
            config['division_column_name'] = 'divisi'
        
        # Basic selected features
        config['selected_features'] = ['divisi', 'segmen', 'witel']
        
        return config

    @staticmethod
    def _get_year_month_filter_expression(model_class, year: int, month: int):
        """Create year-month filter expression based on model structure"""
        config = SchedulerRepository._get_model_config(model_class)
        date_column = config['period_column_name']
        period_type = config['period_column_type']
        
        if period_type == 'datetime':
            return and_(
                extract('year', getattr(model_class, date_column)) == year,
                extract('month', getattr(model_class, date_column)) == month
            )
        else:
            # For integer_yyyymm format, but adapt to actual DB column type
            period_value = f"{year}{month:02d}"
            period_col = getattr(model_class, date_column)

            if hasattr(period_col, 'type'):
                col_type = str(period_col.type).upper()
                logger.debug(f"Kolom {date_column} bertipe: {col_type}")

                # If the actual column is a timestamp/date, use extract year/month
                if 'TIMESTAMP' in col_type or 'DATE' in col_type or 'DATETIME' in col_type:
                    logger.debug(f"Menggunakan extract(year/month) untuk kolom bertipe waktu {date_column}")
                    return and_(
                        extract('year', period_col) == year,
                        extract('month', period_col) == month
                    )

                if 'TEXT' in col_type or 'VARCHAR' in col_type or 'CHAR' in col_type:
                    # Handle berbagai format string
                    period_value_dash = f"{year}-{month:02d}"
                    period_value_slash = f"{year}/{month:02d}"

                    logger.debug(f"Mencoba format: YYYYMM={period_value}, YYYY-MM={period_value_dash}, YYYY/MM={period_value_slash}")

                    return or_(
                        period_col == period_value,           # Format YYYYMM
                        period_col == period_value_dash,      # Format YYYY-MM
                        period_col == period_value_slash      # Format YYYY/MM
                    )

                if 'INTEGER' in col_type or 'INT' in col_type or 'BIGINT' in col_type:
                    logger.debug(f"Kolom {date_column} bertipe integer/BIGINT, langsung bandingkan dengan {period_value}")
                    return period_col == int(period_value)

                # Default: treat as string formats
                period_value_dash = f"{year}-{month:02d}"
                period_value_slash = f"{year}/{month:02d}"
                logger.debug(f"Kolom {date_column} bertipe {col_type}, mencoba berbagai format string")
                return or_(
                    period_col == period_value,
                    period_col == period_value_dash,
                    period_col == period_value_slash
                )
            else:
                # Fallback: coba berbagai format string
                period_value_dash = f"{year}-{month:02d}"
                period_value_slash = f"{year}/{month:02d}"
                logger.debug(f"Tidak bisa menentukan tipe kolom {date_column}, mencoba berbagai format string sebagai fallback")
                return or_(
                    period_col == period_value,           # Format YYYYMM
                    period_col == period_value_dash,      # Format YYYY-MM
                    period_col == period_value_slash      # Format YYYY/MM
                )

    @staticmethod
    def _get_target_column(model_class, config):
        """Get the appropriate target column based on configuration"""
        # Check if target column is already defined in config
        if 'target_column_name' in config:
            return config['target_column_name']
        
        # Fallback to dynamic detection
        if hasattr(model_class, 'real_revenue'):
            return 'real_revenue'
        elif hasattr(model_class, 'revenue_final'):
            return 'revenue_final'
        elif hasattr(model_class, 'revenue'):
            return 'revenue'
        elif hasattr(model_class, 'abonemen'):
            return 'abonemen'
        elif hasattr(model_class, 'rupiah'):
            return 'rupiah'
        elif hasattr(model_class, 'trems_rev'):
            return 'trems_rev'
        elif hasattr(model_class, 'prev_revenue'):
            return 'prev_revenue'
        else:
            return 'revenue'

    @staticmethod
    async def get_data_from_db(model_class, year: Optional[int] = None, month: Optional[int] = None) -> pd.DataFrame:
        """
        Mengambil data untuk 3 periode bulan terakhir dari database.
        Pastikan kolom periode (date_column) memiliki indeks di database untuk performa query yang optimal.
        """
        try:
            logger.info(f"Memulai get_data_from_db dengan parameter: year={year}, month={month}")
            
            config = SchedulerRepository._get_model_config(model_class)
            date_column = config["period_column_name"]
            period_column_type = config.get("period_column_type", "integer_yyyymm")
            selected_features = config.get("selected_features", [])
            
            # Get target column
            target_column = SchedulerRepository._get_target_column(model_class, config)
            logger.info(f"Using target column: {target_column}")

            if model_class.__name__ == "OrderFidb":
                service_type_expr = model_class.get_service_type_expression().label("service_type")
                columns_to_select = [
                    getattr(model_class, date_column),
                    service_type_expr,
                    getattr(model_class, target_column),
                    *[getattr(model_class, feature) for feature in selected_features if hasattr(model_class, feature)]
                ]
            else:
                service_type_label = config.get("service_type_column_name", "service_type")
                # Pastikan kolom service_type ada sebelum digunakan
                if hasattr(model_class, service_type_label):
                    service_type_col = getattr(model_class, service_type_label)
                else:
                    # Fallback ke kolom yang tersedia
                    if hasattr(model_class, 'product_name'):
                        service_type_col = getattr(model_class, 'product_name')
                        service_type_label = 'product_name'
                    elif hasattr(model_class, 'product_group'):
                        service_type_col = getattr(model_class, 'product_group')
                        service_type_label = 'product_group'
                    elif hasattr(model_class, 'variant'):
                        service_type_col = getattr(model_class, 'variant')
                        service_type_label = 'variant'
                    else:
                        # Jika tidak ada kolom service type, gunakan kolom dummy
                        logger.warning(f"Kolom service_type tidak ditemukan di {model_class.__name__}, menggunakan kolom dummy")
                        service_type_col = getattr(model_class, date_column)  # Gunakan date_column sebagai dummy
                        service_type_label = date_column
                
                columns_to_select = [
                    getattr(model_class, date_column),
                    service_type_col,
                    getattr(model_class, target_column),
                    *[getattr(model_class, feature) for feature in selected_features if hasattr(model_class, feature)]
                ]
            
            async with db.async_session() as session:
                if year is None or month is None:
                    logger.info("Tahun atau bulan tidak disediakan, mencari periode terakhir...")
                    
                    # Try to find latest period with data
                    latest_period_query = select(func.max(getattr(model_class, date_column)))
                    if hasattr(model_class, target_column):
                        # Handle type casting untuk kolom revenue yang mungkin TEXT
                        target_col = getattr(model_class, target_column)
                        if hasattr(target_col, 'type'):
                            col_type = str(target_col.type).upper()
                            logger.debug(f"Target kolom {target_column} bertipe: {col_type}")
                            
                            if 'TEXT' in col_type or 'VARCHAR' in col_type or 'CHAR' in col_type:
                                # Cast TEXT ke NUMERIC untuk perbandingan
                                logger.debug(f"Casting target kolom {target_column} dari {col_type} ke NUMERIC untuk perbandingan > 0")
                                latest_period_query = latest_period_query.where(
                                    cast(target_col, Numeric) > 0
                                )
                            else:
                                logger.debug(f"Target kolom {target_column} bertipe {col_type}, langsung bandingkan > 0")
                                latest_period_query = latest_period_query.where(
                                    target_col > 0
                                )
                        else:
                            logger.debug(f"Tidak bisa menentukan tipe target kolom {target_column}, langsung bandingkan > 0")
                            latest_period_query = latest_period_query.where(
                                target_col > 0
                            )
                    
                    result = await session.execute(latest_period_query)
                    latest_period = result.scalar_one_or_none()
                    
                    if latest_period and latest_period is not None:
                        if period_column_type == "datetime":
                            year = latest_period.year
                            month = latest_period.month
                        else:
                            latest_period_str = str(latest_period)
                            # Handle nilai NaN atau invalid
                            if latest_period_str.lower() in ['nan', 'none', 'null', '']:
                                now = datetime.now()
                                year, month = now.year, now.month
                                logger.warning(f"Periode terakhir adalah NaN/None, menggunakan periode saat ini: {month}-{year}")
                            else:
                                try:
                                    # Pastikan format YYYYMM valid
                                    if len(latest_period_str) >= 6:
                                        # Handle format YYYY-MM (2024-05)
                                        if '-' in latest_period_str:
                                            parts = latest_period_str.split('-')
                                            if len(parts) == 2:
                                                year = int(parts[0])
                                                month = int(parts[1])
                                                logger.info(f"Format YYYY-MM terdeteksi: {year}-{month}")
                                            else:
                                                raise ValueError(f"Format YYYY-MM tidak valid: {latest_period_str}")
                                        else:
                                            # Format YYYYMM (202405)
                                            year = int(latest_period_str[:4])
                                            month = int(latest_period_str[4:6])
                                        
                                        # Validasi bulan (1-12)
                                        if month < 1 or month > 12:
                                            logger.warning(f"Bulan tidak valid: {month}, menggunakan bulan saat ini")
                                            month = datetime.now().month
                                        logger.info(f"Periode terakhir ditemukan: {month}-{year}")
                                    else:
                                        raise ValueError(f"Format periode tidak valid: {latest_period_str}")
                                except (ValueError, IndexError) as e:
                                    logger.warning(f"Error parsing periode '{latest_period_str}': {e}, menggunakan periode saat ini")
                                    now = datetime.now()
                                    year, month = now.year, now.month
                    else:
                        now = datetime.now()
                        year, month = now.year, now.month
                        logger.info(f"Tidak ada periode terakhir, menggunakan periode saat ini: {month}-{year}")

                start_period = datetime(year, month, 1)
                p1 = start_period
                p2 = start_period - relativedelta(months=1)
                p3 = start_period - relativedelta(months=2)

                logger.info(f"Mengambil data untuk 3 periode: {p1:%m-%Y}, {p2:%m-%Y}, dan {p3:%m-%Y}")

                filter_expr = or_(
                    SchedulerRepository._get_year_month_filter_expression(model_class, p1.year, p1.month),
                    SchedulerRepository._get_year_month_filter_expression(model_class, p2.year, p2.month),
                    SchedulerRepository._get_year_month_filter_expression(model_class, p3.year, p3.month)
                )
                
                query = select(*columns_to_select).where(filter_expr)
                
                if hasattr(model_class, 'get_data_with_fallback'):
                    logger.info(f"Menggunakan get_data_with_fallback untuk {model_class.__name__} tahun {year}")
                    all_data = await model_class.get_data_with_fallback(session, query, year)
                    if all_data:
                        df = pd.DataFrame(all_data, columns=[col.name if hasattr(col, 'name') else col._label for col in columns_to_select])
                        logger.info(f"DataFrame hasil fallback dibuat dengan shape: {df.shape}")
                        return df
                    else:
                        logger.warning("Tidak ada data yang ditemukan dari fallback.")
                        return pd.DataFrame()
                
                else:
                    logger.debug(f"RAW SQL: {query.compile(dialect=postgresql.dialect(), compile_kwargs={'literal_binds': True})}")
                    
                    logger.info("Menjalankan query database...")
                    result = await session.execute(query)
                    logger.info("Query berhasil dieksekusi.")
                    
                    data_rows = result.mappings().all()
                    
                    if data_rows:
                        logger.info(f"Ditemukan {len(data_rows)} baris data. Mengonversi ke DataFrame...")
                        df = pd.DataFrame(data_rows)
                        logger.info(f"DataFrame berhasil dibuat dengan shape: {df.shape}")
                        return df
                    else:
                        logger.warning("Tidak ada data yang ditemukan untuk periode yang ditentukan.")
                        return pd.DataFrame()
            
        except Exception as e:
            logger.error(f"Error dalam get_data_from_db: Tipe: {type(e).__name__}, Pesan: {str(e)}", exc_info=True)
            raise

    @staticmethod
    async def get_monthly_summary(model_class, target: str) -> List[dict]:
        """
        Menghasilkan ringkasan bulanan dengan mengelompokkan data secara efisien.
        """
        try:
            logger.info(f"Memulai get_monthly_summary dengan target: {target}")
            
            logger.info("Memanggil get_data_from_db...")
            df = await SchedulerRepository.get_data_from_db(model_class=model_class)
            
            if df.empty:
                msg = "Tidak ada data yang tersedia untuk dianalisis."
                logger.warning(msg)
                return [{"summary_text": msg, "product_type": model_class.__name__.lower()}]

            config = SchedulerRepository._get_model_config(model_class)
            service_column = config.get("service_type_column_name", "service_type")
            date_column = config["period_column_name"]
            selected_features = config.get("selected_features", [])

            # Get target column
            target_column = SchedulerRepository._get_target_column(model_class, config)
            logger.info(f"Kolom target untuk analisis: {target_column}")

            # Convert target column to float untuk menghindari error float + Decimal
            if target_column in df.columns:
                try:
                    df[target_column] = pd.to_numeric(df[target_column], errors='coerce').astype(float)
                    logger.debug(f"Kolom {target_column} berhasil dikonversi ke float")
                except Exception as e:
                    logger.warning(f"Gagal mengkonversi kolom {target_column} ke float: {e}")

            if model_class.__name__ == "OrderFidb" and "service_type" not in df.columns:
                if "gl_dss_prod_cat" in df.columns:
                    mapping_dict = {}
                    for new_name, raw_list in getattr(model_class, '_service_type_mapping', {}).items():
                        for raw_name in raw_list:
                            mapping_dict[raw_name] = new_name
                    df["service_type"] = df["gl_dss_prod_cat"].map(mapping_dict).fillna(df["gl_dss_prod_cat"])
                else:
                    logger.warning("Kolom gl_dss_prod_cat tidak ditemukan di DataFrame, groupby mungkin gagal.")

            service_type_label = "service_type" if model_class.__name__ == "OrderFidb" else config.get("service_type_column_name", "service_type")
            
            # Pastikan kolom yang akan digunakan untuk groupby ada di DataFrame
            if service_type_label in df.columns:
                groupby_col = service_type_label
            elif service_column in df.columns:
                groupby_col = service_column
            else:
                # Fallback: cari kolom yang tersedia
                available_cols = [col for col in df.columns if col not in [date_column, target_column]]
                if available_cols:
                    groupby_col = available_cols[0]
                    logger.warning(f"Kolom {service_type_label} dan {service_column} tidak ditemukan, menggunakan {groupby_col} sebagai fallback")
                else:
                    logger.error(f"Tidak ada kolom yang tersedia untuk groupby di {model_class.__name__}")
                    return [{"summary_text": "Tidak ada kolom yang tersedia untuk analisis.", "product_type": model_class.__name__.lower()}]
            logger.info(f"Mengelompokkan data berdasarkan kolom '{groupby_col}'...")
            
            # Filter out NaN values sebelum groupby
            df = df.dropna(subset=[groupby_col])
            if df.empty:
                logger.warning("Tidak ada data valid setelah filtering NaN values")
                return [{"summary_text": "Tidak ada data valid setelah filtering.", "product_type": model_class.__name__.lower()}]
            
            grouped_data = df.groupby(groupby_col)
            
            data_configs = []
            for service_type, service_df in grouped_data:
                if pd.isna(service_type) or service_type == "":
                    continue
                
                service_df_copy = service_df.copy()
                
                date_column = config["period_column_name"]
                try:
                    # Filter out NaN values sebelum parsing
                    service_df_copy = service_df_copy.dropna(subset=[date_column])
                    if service_df_copy.empty:
                        logger.warning(f"Tidak ada data valid untuk service {service_type} setelah filtering NaN values")
                        continue
                    
                    logger.debug(f"Parsing tanggal untuk service {service_type}, kolom {date_column}, dtype: {service_df_copy[date_column].dtype}")
                    
                    # Coba berbagai format parsing tanggal
                    parsed_dates = None
                    
                    # Coba format YYYYMM (integer) - handle dengan lebih baik
                    try:
                        if service_df_copy[date_column].dtype in ['int64', 'float64']:
                            # Kolom sudah numeric, coba format YYYYMM
                            logger.debug(f"Kolom {date_column} bertipe numeric, coba format YYYYMM")
                            # Convert ke string dulu, lalu parse dengan format yang benar
                            period_str = service_df_copy[date_column].astype(str)
                            # Pastikan format YYYYMM valid (6 digit)
                            valid_periods = period_str.str.len() == 6
                            if valid_periods.any():
                                # Parse dengan format yang benar
                                parsed_dates = pd.to_datetime(period_str, format='%Y%m', errors='coerce')
                            else:
                                logger.debug(f"Format YYYYMM tidak valid untuk service {service_type}")
                        else:
                            # Kolom string, coba berbagai format
                            logger.debug(f"Kolom {date_column} bertipe string, coba berbagai format")
                            period_str = service_df_copy[date_column].astype(str)
                            
                            # Coba format YYYY-MM dulu (2024-05)
                            if period_str.str.contains('-').any():
                                logger.debug(f"Format YYYY-MM terdeteksi untuk service {service_type}")
                                parsed_dates = pd.to_datetime(period_str, format='%Y-%m', errors='coerce')
                            # Coba format YYYYMM (202405)
                            elif period_str.str.len().eq(6).any():
                                logger.debug(f"Format YYYYMM terdeteksi untuk service {service_type}")
                                parsed_dates = pd.to_datetime(period_str, format='%Y%m', errors='coerce')
                            else:
                                logger.debug(f"Format tidak dikenali untuk service {service_type}")
                    except Exception as e:
                        logger.debug(f"Format parsing gagal untuk service {service_type}: {e}")
                        pass
                    
                    # Jika format YYYYMM gagal, coba parsing otomatis
                    if parsed_dates is None or parsed_dates.isna().all():
                        try:
                            logger.debug(f"Mencoba parsing otomatis untuk service {service_type}")
                            parsed_dates = pd.to_datetime(service_df_copy[date_column], errors='coerce')
                        except Exception as e:
                            logger.warning(f"Gagal parsing tanggal untuk service {service_type}: {e}")
                            continue
                    
                    if parsed_dates.notna().sum() > 0:
                        service_df_copy['year'] = parsed_dates.dt.year
                        service_df_copy['month'] = parsed_dates.dt.month
                        logger.debug(f"Berhasil parsing tanggal untuk service {service_type}: {parsed_dates.notna().sum()} dari {len(parsed_dates)} baris")
                        
                        # Validasi tahun yang dihasilkan (jangan sampai 1970)
                        valid_years = (service_df_copy['year'] > 2000) & (service_df_copy['year'] < 2030)
                        if not valid_years.any():
                            logger.warning(f"Tahun yang dihasilkan tidak valid untuk service {service_type}: {service_df_copy['year'].unique()}")
                            continue
                        
                        # Update kolom periode dengan tanggal yang sudah di-parse
                        service_df_copy[date_column] = parsed_dates
                        logger.debug(f"Kolom {date_column} diupdate dengan parsed dates untuk service {service_type}")
                    else:
                        logger.warning(f"Tidak ada tanggal valid untuk service {service_type}")
                        continue
                        
                except Exception as e:
                    logger.warning(f"Error parsing tanggal untuk service {service_type}: {e}")
                    continue
                
                # Filter out rows dengan year/month yang NaN
                service_df_copy.dropna(subset=['year', 'month'], inplace=True)
                if service_df_copy.empty:
                    logger.warning(f"Tidak ada data valid untuk service {service_type} setelah parsing tanggal")
                    continue
                    
                service_df_copy[['year', 'month']] = service_df_copy[['year', 'month']].astype(int)

                unique_periods = sorted(service_df_copy[['year', 'month']].drop_duplicates().values.tolist(), reverse=True)

                if len(unique_periods) < 2:
                    logger.warning(f"Data untuk layanan {service_type} tidak cukup untuk perbandingan (hanya ada {len(unique_periods)} periode).")
                    continue
                    
                curr_year, curr_month = unique_periods[0]
                prev_year, prev_month = unique_periods[1]

                service_config = {
                    'df': service_df_copy, 
                    'service_type': service_type,
                    'service_column': service_column,
                    'date_column': date_column,
                    'selected_features': config.get("selected_features", []),
                    'current_period': {'year': curr_year, 'month': curr_month}, 
                    'previous_period': {'year': prev_year, 'month': prev_month} 
                }
                data_configs.append(service_config)
                logger.info(f"Konfigurasi telah disiapkan untuk service_type: {service_type} ({len(service_df)} baris) untuk periode {curr_month}-{curr_year}")
            
            if not data_configs:
                msg = "Tidak ada data valid setelah pengelompokan."
                logger.warning(msg)
                return [{"summary_text": msg, "product_type": model_class.__name__.lower()}]

            logger.info(f"Total konfigurasi yang akan diproses: {len(data_configs)}. Memulai generate_monthly_summary_schedule...")
            results = generate_monthly_summary_schedule(data_configs, target_column)
            logger.info(f"Generate summary selesai, jumlah hasil: {len(results) if results else 0}")
            
            if not results:
                return [{"summary_text": "Tidak ada perubahan signifikan yang terdeteksi.", "product_type": model_class.__name__.lower()}]
            
            return [
                {"summary_text": result["summary_text"], "product_type": model_class.__name__.lower(), "service_type": result["service_type"]}
                for result in results
            ]
            
        except Exception as e:
            logger.error(f"Error dalam get_monthly_summary: Tipe: {type(e).__name__}, Pesan: {str(e)}", exc_info=True)
            raise
