import pandas as pd
import numpy as np

def keyword_devisi(
    df, target, year, month,
    date_column,
    selected_features=None,
    division_column=None, division_name=None,
    service_type=None, service_column=None,
    product_type=None
):
    df = df.copy()

    # Validasi kolom wajib
    required_cols = [date_column, target]
    if service_column:
        required_cols.append(service_column)
    if division_column:
        required_cols.append(division_column)
    for col in required_cols:
        if col not in df.columns:
            raise ValueError(f"Kolom '{col}' tidak ditemukan dalam data.")

    df = df.copy() 
    try:
        parsed_dates = pd.to_datetime(df[date_column], format='%Y%m', errors='coerce')

        if parsed_dates.notna().sum() > 0:
            df['year'] = parsed_dates.dt.year
            df['month'] = parsed_dates.dt.month
        else:
            raise ValueError("Format YYYYMM tidak cocok")

    except (ValueError, TypeError):
        parsed_dates = pd.to_datetime(df[date_column], errors='coerce')
        df['year'] = parsed_dates.dt.year
        df['month'] = parsed_dates.dt.month

    df.dropna(subset=['year', 'month'], inplace=True)
    df[['year', 'month']] = df[['year', 'month']].astype(int)

    # Filter data periode saat ini
    if month is not None:
        # Analisis per bulan
        curr_df = df[(df['year'] == year) & (df['month'] == month)]
        # Filter data bulan sebelumnya
        prev_year, prev_month = (year - 1, 12) if month == 1 else (year, month - 1)
        prev_df = df[(df['year'] == prev_year) & (df['month'] == prev_month)]
        bulan_str = pd.to_datetime(f"{year}-{month:02}-01").strftime('%B %Y')
        prev_bulan_str = pd.to_datetime(f"{prev_year}-{prev_month:02}-01").strftime('%B %Y')
    else:
        # Analisis per tahun
        curr_df = df[df['year'] == year]
        prev_df = df[df['year'] == year - 1]
        bulan_str = f"tahun {year}"
        prev_bulan_str = f"tahun {year-1}"

    # Terapkan filter service_type dan division jika ada
    if service_column and service_type:
        curr_df = curr_df[curr_df[service_column] == service_type]
        prev_df = prev_df[prev_df[service_column] == service_type]
    if division_column and division_name:
        curr_df = curr_df[curr_df[division_column] == division_name]
        prev_df = prev_df[prev_df[division_column] == division_name]

    curr_total = curr_df[target].sum()
    prev_total = prev_df[target].sum()
    delta = curr_total - prev_total
    rupiah = f"sekitar Rp{curr_total:,.0f}".replace(",", ".")

    # Membuat summary text
    summary_parts = [f"Total {target}"]
    if service_column and service_type:
        summary_parts.append(f"untuk layanan {service_type}")
    if division_name:
        summary_parts.append(f"untuk divisi {division_name}")
    if product_type:
        summary_parts.append(f"pada produk {product_type}")
    summary_parts.append(f"pada {bulan_str} adalah {rupiah}.")
    summary = " ".join(summary_parts)

    # Analisis perubahan
    if prev_df.empty:
        summary += f" Tidak dapat dibandingkan dengan {prev_bulan_str} karena data tidak tersedia."
    else:
        if prev_total == 0:
            summary += f" Tidak dapat menghitung perubahan karena data {prev_bulan_str} nol."
        else:
            diff = curr_total - prev_total
            pct = (diff / prev_total) * 100
            arah = "kenaikan" if diff > 0 else "penurunan" if diff < 0 else "stabil"
            rupiah_diff = f"sekitar Rp{abs(diff):,.0f}".replace(",", ".")
            summary += f" Terjadi {arah} sebesar {abs(pct):.1f}% ({rupiah_diff}) dibandingkan {prev_bulan_str}."

    # Analisis penyebab
    summary += "\n\nKemungkinan penyebab perubahan:\n"
    if selected_features:
        changes = []
        for feat in selected_features:
            if feat in df.columns:
                # For curr_val
                modes_curr = curr_df[feat].mode()
                curr_val = modes_curr[0] if not modes_curr.empty else "-"

                # For prev_val
                modes_prev = prev_df[feat].mode()
                prev_val = modes_prev[0] if not modes_prev.empty else "-"
                if curr_val != prev_val:
                    changes.append(f" - Perubahan {feat} dari '{prev_val}' menjadi '{curr_val}'")
        if changes:
            summary += "\n".join(changes)
        else:
            summary += " - Tidak terdapat perubahan signifikan pada fitur terpilih."
    else:
        summary += " - Tidak ada fitur pembeda (selected_features) yang dianalisis."

    return {
        "year": year,
        "month": month,
        "service_type": service_type,
        "service_column": service_column,
        "revenue_current": curr_total,
        "revenue_last": prev_total,
        "delta": delta,
        "trend": arah,
        "division": division_name,
        "target": target,
        "product_type": product_type,
        "summary_text": summary,
        "long_summary": summary
    }