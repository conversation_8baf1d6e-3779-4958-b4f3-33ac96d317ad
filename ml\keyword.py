import pandas as pd
import numpy as np

def keyword(df, target, year, month, service_type, date_column, selected_features=None, service_column=None, product_type=None):
    df = df.copy()

    if date_column not in df.columns:
        raise ValueError(f"Kolom '{date_column}' tidak ditemukan dalam data.")
    if target not in df.columns:
        raise ValueError(f"Kolom '{target}' tidak ditemukan dalam data.")
    if service_column not in df.columns:
        raise ValueError(f"Kolom '{service_column}' tidak ditemukan dalam data.")

    # Konversi kolom tanggal
    try:
        parsed_dates = pd.to_datetime(df[date_column], format='%Y%m', errors='coerce')
        if parsed_dates.notna().sum() > 0:
            df['year'] = parsed_dates.dt.year
            df['month'] = parsed_dates.dt.month
        else:
            raise ValueError("Format YYYYMM tidak cocok.")
    except Exception:
        parsed_dates = pd.to_datetime(df[date_column], errors='coerce')
        df['year'] = parsed_dates.dt.year
        df['month'] = parsed_dates.dt.month

    df.dropna(subset=['year', 'month'], inplace=True)
    df[['year', 'month']] = df[['year', 'month']].astype(int)

    # Tentukan periode saat ini dan sebelumnya
    curr_period_str = pd.to_datetime(f"{year}-{month:02}-01").strftime('%B %Y')
    if month == 1:
        prev_year, prev_month = year - 1, 12
    else:
        prev_year, prev_month = year, month - 1
    prev_period_str = pd.to_datetime(f"{prev_year}-{prev_month:02}-01").strftime('%B %Y')

    # Filter data
    df_curr = df[(df['year'] == year) & (df['month'] == month)]
    df_last = df[(df['year'] == prev_year) & (df['month'] == prev_month)]

    if service_type:
        df_curr = df_curr[df_curr[service_column] == service_type]
        df_last = df_last[df_last[service_column] == service_type]

    revenue_curr = df_curr[target].sum()
    revenue_last = df_last[target].sum()
    delta = revenue_curr - revenue_last
    trend = "naik" if delta > 0 else "turun" if delta < 0 else "stabil"
    delta_percent = abs(delta) / revenue_last * 100 if revenue_last else 0

    # Perubahan fitur
    change_descriptions = []
    if selected_features:
        for feat in selected_features:
            if feat in df.columns:
                top_last = df_last[feat].mode().iloc[0] if not df_last[feat].mode().empty else "-"
                top_curr = df_curr[feat].mode().iloc[0] if not df_curr[feat].mode().empty else "-"
                if top_last != top_curr:
                    change_descriptions.append(f"perubahan *{feat}* dari **{top_last}** menjadi **{top_curr}**")
                else:
                    change_descriptions.append(f"tidak ada perubahan *{feat}*, tetap **{top_curr}**")
    else:
        change_descriptions.append("tidak ada fitur pembeda yang dianalisis")

    # Ranking divisi
    if 'divisi' in df_curr.columns:
        group = df_curr.groupby("divisi")[target].sum().reset_index()
        group_sorted = group.sort_values(by=target, ascending=False)
        total = group_sorted[target].sum()

        top3 = group_sorted.head(3)
        bottom3 = group_sorted.tail(3)

        top3_text = ", ".join([
            f"**{row['divisi']}** (Rp {int(row[target]):,} | {row[target]/total*100:.2f}%)"
            for _, row in top3.iterrows()
        ])
        bottom3_text = ", ".join([
            f"**{row['divisi']}** (Rp {int(row[target]):,} | {row[target]/total*100:.2f}%)"
            for _, row in bottom3.iterrows()
        ])
    else:
        top3_text = bottom3_text = "Data divisi tidak tersedia."

    long_summary = (
        f"Berdasarkan hasil analisis *{target}*"
        f"{f' pada produk *{service_type}*' if service_type else ''} pada {curr_period_str} mengalami **{trend}** "
        f"sebesar **{delta_percent:.2f}%** sejumlah **Rp. {int(abs(delta)):,}** dibandingkan {prev_period_str}. "
        f"Kemungkinan penyebab **{trend}** adalah karena " + ", dan ".join(change_descriptions) + ". "
        f"Ranking 3 teratas dengan nilai *{target}* terbesar adalah {top3_text}, "
        f"dan ranking 3 terbawah dengan nilai *{target}* terkecil adalah {bottom3_text}."
    )
    
    summary = (
        f"Analisis *{target}*"
        f"{f' produk *{service_type}*' if service_type else ''} pada {curr_period_str}."
        f" Tren **{trend}**."
        f" Periode sebelumnya, {prev_period_str}. "
    )
    
    # new_summary = {
    #     "year": year,
    #     "month": month,
    #     "service_type": service_type,
    #     "target": target,
    #     "product_type": product_type,
    #     "trend": trend,
    #     "change_descriptions": change_descriptions,
    #     "prev_period": prev_period_str,
    #     "curr_period": curr_period_str
    # }

    return {
        "year": year,
        "month": month,
        "service_type": service_type,
        "target": target,
        "trend": trend,
        "delta": delta,
        "product_type": product_type,
        "revenue_current": revenue_curr,
        "revenue_last": revenue_last,
        "summary_text": summary,
        "long_summary": long_summary,
    }