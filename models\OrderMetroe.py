from sqlalchemy import Column, String, Integer, Float, DateTime, Boolean, select, text, func, and_, Index
from core.database import Base, db
import logging

logger = logging.getLogger(__name__)

class OrderMetroe(Base):
    __tablename__ = "dbv_ebis_v_amdes_nf_aktif_metroe_final"
    __table_args__ = (
        Index('idx_metroe_periode', 'periode'),
        Index('idx_metroe_periode_service', 'periode', 'service_type'),
        Index('idx_metroe_divisi', 'divisi'),
        {'schema': 'satgas_ai'}
    )

    __additional_tablename__ = "dbv_wins_v_amdes_nf_aktif_metroe_final"

    periode = Column(String , primary_key=True)
    customer_ref = Column(String)
    account_num = Column(String)
    account_name = Column(String)
    business_share = Column(String)
    divisi = Column(String)
    segmen = Column(String)
    currency_code = Column(String)
    product_label = Column(String)
    cust_order_num = Column(String)
    install = Column(Float)
    abonemen = Column(Float)
    circuit_id = Column(String)
    circuit_name = Column(String)
    circuit_speed = Column(Float)
    value_bw_mbps = Column(Float)
    bandwidth_a = Column(Float)
    bandwidth_b = Column(Float)
    product_name = Column(String)
    product_group = Column(String)
    product_seq = Column(String)
    status_reason_txt = Column(String)
    effective_dtm = Column(DateTime)
    tariff_name = Column(String)
    ppn = Column(Float)
    tgl_suspend = Column(DateTime)
    update_dat = Column(DateTime)
    tipe_metroe = Column(String)
    tipe_metroe_0 = Column(String)
    tipe_metroe_0_new = Column(String)
    topologi_metroe_old = Column(String)
    topologi_metroe = Column(String)
    qos = Column(String)
    qos_1 = Column(String)
    billcomdate_1 = Column(DateTime)
    tarif_total = Column(Float)
    revenue = Column(Float)
    diskon_rp = Column(Float)
    diskon_percent = Column(Float)
    hjm = Column(Float)
    margin_rev_hjm_rp = Column(Float)
    margin_rev_hjm_percent = Column(Float)
    kat_hjm = Column(String)
    suspend = Column(String)
    tarif_total_dws = Column(Float)
    agree_name = Column(String)
    service_witel = Column(String)
    service_region = Column(String)
    zona_tarif_1 = Column(String)
    treg_new = Column(Float)
    agree_start_date = Column(DateTime)
    agree_end_date = Column(DateTime)
    li_status_date = Column(DateTime)
    custaccntname = Column(String)
    order_subtype = Column(String)
    sto = Column(String)
    sto_code = Column(String)
    metro_transit = Column(String)
    top_cc_des = Column(String)
    sero_id = Column(String)
    service_type = Column(String)
    bandwidth = Column(Float)
    zona_tarif = Column(String)
    fitur = Column(String)
    last_mile = Column(String)
    shiptoparty = Column(String)
    name1sold = Column(String)
    name1ship1 = Column(String)
    createdon = Column(DateTime)
    street1sold = Column(String)
    notelp = Column(String)
    treg = Column(String)
    witel = Column(String)
    billcomdate = Column(DateTime)
    cntrc_sdate = Column(DateTime)
    cntrc_edate = Column(DateTime)
    kota_asal = Column(String)
    kota_tujuan = Column(String)
    acces_type = Column(String)
    asal_order = Column(String)
    datacenter = Column(String)
    fitur_jumbo = Column(String)
    fitur_mac = Column(String)
    fitur_vlan = Column(String)
    ikg = Column(String)
    jumbo = Column(String)
    ldatacenter = Column(String)
    mac = Column(String)
    mitra_bisnis = Column(String)
    paket_metrotype = Column(String)
    port_ethernet = Column(String)
    sid_link_metro = Column(String)
    sid_metro = Column(String)
    tarif_lastmile = Column(Float)
    vlan_num = Column(String)
    glob_area1 = Column(String)
    inter_area_1 = Column(String)
    lokasi_glob_1 = Column(String)
    lokasi_inter_1 = Column(String)
    loop = Column(String)
    marea1 = Column(String)
    marea2 = Column(String)
    marea3 = Column(String)
    slg = Column(String)
    nip_nas = Column(String)
    top_cc_dgs = Column(String)
    service_type_ncx = Column(String)
    is_dual_homing = Column(String)
    epics_fttm = Column(String)
    epics_lastmile = Column(String)
    epics_tarif_fttm = Column(Float)
    epics_status_lastmile = Column(String)
    epics_kat_customer = Column(String)
    epics_exclude_note = Column(String)
    epics_exclude_detail = Column(String)
    vc_id = Column(String)
    subsegment = Column(String)
    cust_cndc = Column(String)
    sto_name_cndc = Column(String)
    pull_thru_cndc = Column(String)
    tarif_bw_performance = Column(Float)
    pull_thru_cndc_alter = Column(String)
    uim_latitude = Column(Float)
    uim_longitude = Column(Float)
    uim_accesstechnology = Column(String)
    uim_bandwidth = Column(Float)
    uim_networktopology = Column(String)
    uim_service_point = Column(String)
    uim_target = Column(String)
    uim_access_device = Column(String)
    uim_uplinkport = Column(String)
    uim_metro_access_device = Column(String)
    uim_me_access_mtu = Column(String)
    uim_downlink = Column(String)
    uim_vcid = Column(String)
    uim_nte_device = Column(String)
    uim_nte_type = Column(String)
    uim_vlan = Column(String)
    qos_ncx = Column(String)
    flag_sdwan = Column(String)
    network_id = Column(String)
    nip_nas_cbase = Column(String)
    standard_name_cbase = Column(String)
    segmen_cbase = Column(String)
    is_hub_or_core = Column(String)
    bill_mny = Column(String)
    cx_termin_value = Column(Float)
    cx_termin_flg = Column(String)
    termin_seq_flg = Column(String)
    cx_otc_amount = Column(Float)
    termin_hjm = Column(Float)
    termin_tarif = Column(Float)
    cntrc_duration = Column(Integer)
    jml_sequence = Column(Integer)
    revenue_final = Column(Float)
    hjm_final = Column(Float)
    tarif_final = Column(Float)
    is_ao_period = Column(String)
    flag_otc = Column(String)
    otc_seq_flg = Column(String)
    revenue_otc = Column(Float)
    tarif_otc = Column(Float)
    hjm_otc = Column(Float)
    term_payment = Column(String)
    revenue_mrc = Column(Float)
    tarif_mrc = Column(Float)
    hjm_mrc = Column(Float)
    mrc_seq_flg = Column(String)
    flag_top = Column(String)
    metroe_data_center = Column(String)
    reg_new = Column(Float)
    witel_new = Column(String)

    @classmethod
    async def get_data_with_fallback(cls, session, query, year: int):
        """Scan data dari kedua tabel dan gabungkan hasilnya"""
        try:
            all_data = []
            
            # Scan dari tabel utama (ebis)
            logger.info(f"Scanning data dari {cls.__tablename__}")
            result = await session.execute(query)
            ebis_data = result.all()
            if ebis_data:
                logger.info(f"Ditemukan {len(ebis_data)} data dari {cls.__tablename__}")
                all_data.extend(ebis_data)
            
            # Scan dari tabel tambahan (wins)
            logger.info(f"Scanning data dari {cls.__additional_tablename__}")
            
            # Ambil query string dan parameter dari query asli
            compiled_query = query.compile(compile_kwargs={"literal_binds": True})
            query_str = str(compiled_query)
            params = compiled_query.params
            
            # Ganti nama tabel dalam query string
            additional_query_str = query_str.replace(
                f"{cls.__table_args__[3]['schema']}.{cls.__tablename__}", 
                f"{cls.__table_args__[3]['schema']}.{cls.__additional_tablename__}"
            )
            
            # Buat query baru menggunakan text()
            additional_query = text(additional_query_str)
            
            # Jalankan query dengan parameter yang sama
            result = await session.execute(additional_query, params)
            wins_data = result.all()
            if wins_data:
                logger.info(f"Ditemukan {len(wins_data)} data dari {cls.__additional_tablename__}")
                all_data.extend(wins_data)
            
            # Log total data yang ditemukan
            total_data = len(all_data)
            if total_data > 0:
                logger.info(f"Total data yang ditemukan dari kedua tabel: {total_data}")
            else:
                logger.warning("Tidak ada data yang ditemukan dari kedua tabel")
            
            return all_data
            
        except Exception as e:
            logger.error(f"Error dalam get_data_with_fallback: {str(e)}", exc_info=True)
            raise 