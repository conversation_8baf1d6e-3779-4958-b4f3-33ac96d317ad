import pandas as pd
import numpy as np

def keyword(df, target, year, month, service_type, date_column, selected_features=None, service_column=None, product_type=None):
    df = df.copy()

    if date_column not in df.columns:
        raise ValueError(f"Kolom '{date_column}' tidak ditemukan dalam data.")
    if target not in df.columns:
        raise ValueError(f"Kolom '{target}' tidak ditemukan dalam data.")
    if service_column not in df.columns:
        raise ValueError(f"Kolom '{service_column}' tidak ditemukan dalam data.")


    df = df.copy() 
    try:
        parsed_dates = pd.to_datetime(df[date_column], format='%Y%m', errors='coerce')

        if parsed_dates.notna().sum() > 0:
            df['year'] = parsed_dates.dt.year
            df['month'] = parsed_dates.dt.month
        else:
            raise ValueError("Format YYYYMM tidak cocok")

    except (ValueError, TypeError):

        parsed_dates = pd.to_datetime(df[date_column], errors='coerce')
        df['year'] = parsed_dates.dt.year
        df['month'] = parsed_dates.dt.month

    df.dropna(subset=['year', 'month'], inplace=True)
    df[['year', 'month']] = df[['year', 'month']].astype(int)

    # Data periode saat ini
    if service_type is not None:
        if month is not None:
            # Analisis per bulan
            curr_df = df[(df['year'] == year) & (df['month'] == month) & (df[service_column] == service_type)]
            # Data bulan sebelumnya
            if month == 1:
                prev_month = 12
                prev_year = year - 1
            else:
                prev_month = month - 1
                prev_year = year
            prev_df = df[(df['year'] == prev_year) & (df['month'] == prev_month) & (df[service_column] == service_type)]
            bulan_str = pd.to_datetime(f"{year}-{month:02}-01").strftime('%B %Y')
            prev_bulan_str = pd.to_datetime(f"{prev_year}-{prev_month:02}-01").strftime('%B %Y')
        else:
            # Analisis per tahun
            curr_df = df[(df['year'] == year) & (df[service_column] == service_type)]
            prev_df = df[(df['year'] == year - 1) & (df[service_column] == service_type)]
            bulan_str = f"tahun {year}"
            prev_bulan_str = f"tahun {year-1}"
    else:
        if month is not None:
            # Analisis per bulan
            curr_df = df[(df['year'] == year) & (df['month'] == month)]
            # Data bulan sebelumnya
            if month == 1:
                prev_month = 12
                prev_year = year - 1
            else:
                prev_month = month - 1
                prev_year = year
            prev_df = df[(df['year'] == prev_year) & (df['month'] == prev_month)]
            bulan_str = pd.to_datetime(f"{year}-{month:02}-01").strftime('%B %Y')
            prev_bulan_str = pd.to_datetime(f"{prev_year}-{prev_month:02}-01").strftime('%B %Y')
        else:
            # Analisis per tahun
            curr_df = df[df['year'] == year]
            prev_df = df[df['year'] == year - 1]
            bulan_str = f"tahun {year}"
            prev_bulan_str = f"tahun {year-1}"

    curr_total = curr_df[target].sum()
    prev_total = prev_df[target].sum()
    rupiah = f"sekitar Rp{curr_total:,.0f}".replace(",", ".")

    # Membuat summary text dengan product type
    summary_parts = [f"Total {target}"]
    if service_type is not None:
        summary_parts.append(f"untuk layanan {service_type}")
    if product_type is not None:
        summary_parts.append(f"pada produk {product_type}")
    summary_parts.append(f"pada {bulan_str} adalah {rupiah}.")
    summary = " ".join(summary_parts)

    # Analisis perubahan
    if prev_df.empty:
        summary += f" Tidak dapat dibandingkan dengan {prev_bulan_str} karena data tidak tersedia."
    else:
        if prev_total == 0:
            summary += f" Tidak dapat menghitung perubahan karena data {prev_bulan_str} nol."
        else:
            diff = curr_total - prev_total
            pct = (diff / prev_total) * 100
            arah = "kenaikan" if diff > 0 else "penurunan" if diff < 0 else "stabil"
            rupiah_diff = f"sekitar Rp{abs(diff):,.0f}".replace(",", ".")
            summary += f" Terjadi {arah} sebesar {abs(pct):.1f}% ({rupiah_diff}) dibandingkan {prev_bulan_str}."

    # Analisis fitur penyebab (jika diminta)
    summary += "\n\nKemungkinan penyebab perubahan:\n"
    if selected_features:
        changes = []
        for feat in selected_features:
            if feat in df.columns:
                curr_mode = curr_df[feat].mode()
                prev_mode = prev_df[feat].mode()

                # Check if the mode Series is not empty before accessing the first element
                curr_val = curr_mode.iloc[0] if not curr_mode.empty else "-"
                prev_val = prev_mode.iloc[0] if not prev_mode.empty else "-"
                if curr_val != prev_val:
                    changes.append(f" - Perubahan {feat} dari '{prev_val}' menjadi '{curr_val}'")
        if changes:
            summary += "\n".join(changes)
        else:
            summary += " - Tidak terdapat perubahan signifikan pada fitur terpilih."
    else:
        summary += " - Tidak ada fitur pembeda (selected_features) yang dianalisis."

    return {
        "year": year,
        "month": month,
        "service_type": service_type,
        "target": target,
        "product_type": product_type,
        "summary_text": summary
    }