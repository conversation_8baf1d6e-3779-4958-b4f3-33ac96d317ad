import json
from fastapi import APIRouter, HTTPException, status
from typing import Optional, Literal, List

import httpx
from repository.SchedulerRepository import SchedulerRepository
from models.OrderAstinet import OrderAstinet
# from models.OrderIpTransit import OrderIpTransit
from models.OrderMetroe import Order<PERSON>etroe
from models.OrderSlDigital import OrderSlDigital
from models.OrderSlwdm import OrderSlwdm
from models.OrderVpnip import OrderVpnip
from models.OrderFidb import OrderFidb
from models.OrderVoicePots import OrderVoicePots
from models.OrderNeuCentrixAbonemen import OrderNeuCentrixAbonemen
from models.OrderNeuCentrixRevenue import OrderNeuCentrixRevenue
from models.OrderGlobalLink import OrderGlobalLink
from models.OrderHsiB2b import OrderHsiB2b
from models.OrderChurnDatacomm import OrderChurnDatacomm
from models.OrderChurnInternetIpTransit import OrderChurnInternetIpTransit
from models.OrderChurnInternetAstinet import OrderChurnInternetAstinet
from models.OrderSdwanConnectivity import OrderSdwanConnectivity
from models.OrderSdwanService import OrderSdwanService
from core.chatbot import ChatbotService
from schemas.common import SummaryResponse, SummaryInsightResponse, SummaryInsightItem
from datetime import datetime
from models.summary_insight import SummaryInsight
from core.database import db
from sqlalchemy import desc, select
import logging
import re

from settings import CHATBOT_ENDPOINT, CHATBOT_TOKEN

logger = logging.getLogger(__name__)

router = APIRouter()

# Helper function untuk testing regex patterns
def test_regex_patterns(text: str) -> dict:
    """
    Test semua regex patterns terhadap teks yang diberikan
    """
    patterns = [
        r'(penurunan|peningkatan)\s+sebesar\s+(-?[\d.,]+)%',
        r'(penurunan|peningkatan)\s+(-?[\d.,]+)%',
        r'mengalami\s+(penurunan|peningkatan)\s+(-?[\d.,]+)%',
        r'(penurunan|peningkatan)\s+revenue\s+sebesar\s+(-?[\d.,]+)%',
        r'(penurunan|peningkatan)\s+revenue\s+(-?[\d.,]+)%',
        r'mengalami\s+(penurunan|peningkatan)\s+revenue\s+sebesar\s+(-?[\d.,]+)%',
        r'mengalami\s+(penurunan|peningkatan)\s+revenue\s+(-?[\d.,]+)%',
        r'mengalami\s+stabil\s+revenue\s+sebesar\s+(-?[\d.,]+)%',
        r'stabil\s+revenue\s+sebesar\s+(-?[\d.,]+)%'
    ]
    
    results = {}
    for i, pattern in enumerate(patterns):
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            results[f"pattern_{i+1}"] = {
                "matched": True,
                "groups": match.groups(),
                "pattern": pattern
            }
        else:
            results[f"pattern_{i+1}"] = {
                "matched": False,
                "pattern": pattern
            }
    
    return results

# Comprehensive model mapping based on product configuration
model_map = {
    #"astinet": OrderAstinet,
    # "metro_ethernet": OrderMetroe,
    # "slwdm": OrderSlwdm,
    # "vpn_ip": OrderVpnip,
    # "sap": OrderFidb,
    #"voice_pots": OrderVoicePots,
    # "neucentrix_abonemen": OrderNeuCentrixAbonemen,
    # "neucentrix_revenue": OrderNeuCentrixRevenue,
    # "sl_domestik": OrderSlDigital,
    # "sdwan_connectivity": OrderSdwanConnectivity,
    # "sdwan_service": OrderSdwanService,
    # "hsi_b2b": OrderHsiB2b,
    "churn_datacomm": OrderChurnDatacomm,
    # "churn_internet_iptransit": OrderChurnInternetIpTransit,
    # "churn_internet_astinet": OrderChurnInternetAstinet,
}

@router.post("/insight", response_model=SummaryInsightResponse)
async def get_scheduled_insight():
    try:
        all_summaries = []
        # Hitung bulan dan tahun sebelumnya
        current_date = datetime.now()
        if current_date.month == 1:
            prev_month = 12
            prev_year = current_date.year - 1
        else:
            prev_month = current_date.month - 1
            prev_year = current_date.year
        prev_month_str = str(prev_month).zfill(2)
        prev_year_str = str(prev_year)
        
        for product_type, model_class in model_map.items():
            try:
                logger.info(f"Memproses model {product_type}...")
                
                summaries = await SchedulerRepository.get_monthly_summary(
                    model_class=model_class,
                    target='revenue' 
                )
                
                if summaries:
                    for summary in summaries:
                        # Jika summary sudah punya month/year, jangan timpa
                        if 'month' not in summary:
                            summary['month'] = prev_month_str
                        if 'year' not in summary:
                            summary['year'] = prev_year_str
                        summary['product_type'] = product_type
                    all_summaries.extend(summaries)
                    logger.info(f"Ringkasan untuk {product_type}: {len(summaries)} item ditambahkan")
                
            except Exception as e:
                logger.error(f"Error saat memproses model {product_type}: {str(e)}")
                continue
        
        filtered_summaries = []
        for summary in all_summaries:
            summary_text = summary.get('summary_text', '')
            logger.info(f"Processing summary text: {summary_text[:150]}...")
            
            # Multiple regex patterns untuk mendeteksi perubahan revenue
            patterns = [
                r'(penurunan|peningkatan)\s+sebesar\s+(-?[\d.,]+)%',
                r'(penurunan|peningkatan)\s+(-?[\d.,]+)%',
                r'mengalami\s+(penurunan|peningkatan)\s+(-?[\d.,]+)%',
                r'(penurunan|peningkatan)\s+revenue\s+sebesar\s+(-?[\d.,]+)%',
                r'(penurunan|peningkatan)\s+revenue\s+(-?[\d.,]+)%',
                r'mengalami\s+(penurunan|peningkatan)\s+revenue\s+sebesar\s+(-?[\d.,]+)%',
                r'mengalami\s+(penurunan|peningkatan)\s+revenue\s+(-?[\d.,]+)%',
                r'mengalami\s+stabil\s+revenue\s+sebesar\s+(-?[\d.,]+)%',
                r'stabil\s+revenue\s+sebesar\s+(-?[\d.,]+)%'
            ]
            
            match = None
            for pattern in patterns:
                match = re.search(pattern, summary_text, re.IGNORECASE)
                if match:
                    logger.info(f"Pattern matched: {pattern}")
                    break
            
            if match:
                # Handle kasus khusus untuk pattern stabil
                if 'stabil' in match.group(0):
                    change_type = 'stabil'
                    pct_str = match.group(1).replace(',', '.')
                else:
                    change_type = match.group(1).lower()
                    pct_str = match.group(2).replace(',', '.')
                
                logger.info(f"Regex match found: change_type='{change_type}', percentage='{pct_str}' from text: '{summary_text[:100]}...'")
                try:
                    pct = float(pct_str)
                    logger.info(f"Parsed percentage: {pct} (type: {type(pct)})")
                    
                    # Handle berbagai jenis perubahan
                    if change_type == "penurunan" and pct > 10:
                        filtered_summaries.append(summary)
                        logger.info(f"✅ Menambahkan summary dengan penurunan {pct}%: {summary_text[:100]}...")
                    elif change_type == "peningkatan" and pct > 10:
                        logger.info(f"ℹ️ Skip summary: peningkatan {pct}% (bukan penurunan): {summary_text[:100]}...")
                    elif change_type == "stabil":
                        logger.info(f"ℹ️ Skip summary: stabil {pct}% (bukan perubahan): {summary_text[:100]}...")
                    else:
                        logger.info(f"❌ Skip summary: {change_type} {pct}% (bukan penurunan >10%): {summary_text[:100]}...")
                except ValueError:
                    logger.warning(f"Tidak bisa parse persentase dari: {pct_str}")
                    continue
            else:
                if any(keyword in summary_text.lower() for keyword in ['tidak ada data', 'tidak ada perubahan', 'stabil']):
                    logger.info(f"ℹ️ Skip summary (tidak ada perubahan/data): {summary_text[:100]}...")
                else:
                    logger.warning(f"❌ Regex tidak match untuk text: {summary_text[:100]}...")
                    for i, pattern in enumerate(patterns):
                        partial_match = re.search(pattern.replace(r'(-?[\d.,]+)%', r'.*'), summary_text, re.IGNORECASE)
                        if partial_match:
                            logger.info(f"🔍 Pattern {i+1} hampir match: {pattern}")
        
        if not filtered_summaries:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tidak ada data yang memenuhi syarat penurunan >10% untuk dianalisis"
            )
        
        saved_insights = []
        for summary_data in filtered_summaries:
            try:
                async with db.async_session() as session:
                    query = select(SummaryInsight).where(
                        SummaryInsight.product_type == summary_data['product_type'],
                        SummaryInsight.service_type == summary_data.get('service_type'),
                        SummaryInsight.month == summary_data['month'],
                        SummaryInsight.year == summary_data['year'],
                        SummaryInsight.isdel == False
                    )
                    result = await session.execute(query)
                    existing = result.scalar_one_or_none()
                    if existing:
                        logger.info(f"Data untuk {summary_data['product_type']} - {summary_data.get('service_type')} bulan {summary_data['month']}-{summary_data['year']} sudah ada, skip insert.")
                        continue
                insight = await ChatbotService.get_insight(summary_data['summary_text'])
                if not insight:
                    insight = "Tidak dapat mendapatkan insight saat ini."
                    logger.warning(f"Tidak berhasil mendapatkan insight untuk {summary_data['product_type']}")
                summary_data['insight'] = insight
                async with db.async_session() as session:
                    summary_insight = SummaryInsight(
                        summary_text=summary_data['summary_text'],
                        insight=insight,
                        product_type=summary_data['product_type'],
                        service_type=summary_data.get('service_type'),
                        month=summary_data['month'],
                        year=summary_data['year']
                    )
                    session.add(summary_insight)
                    await session.commit()
                    saved_insights.append(SummaryInsightItem(
                        summary_text=summary_data['summary_text'],
                        insight=insight,
                        product_type=summary_data['product_type'],
                        service_type=summary_data.get('service_type'),
                        month=summary_data['month'],
                        year=summary_data['year'],
                        created_at=summary_insight.created_at
                    ))
                    logger.info(f"Berhasil menyimpan data untuk {summary_data['product_type']} ke database")
                    
            except Exception as e:
                logger.error(f"Error saat menyimpan data untuk {summary_data['product_type']}: {str(e)}")
                saved_insights.append(SummaryInsightItem(
                    summary_text=summary_data['summary_text'],
                    insight=summary_data.get('insight', 'Error saat mendapatkan insight'),
                    product_type=summary_data['product_type'],
                    service_type=summary_data.get('service_type'),
                    month=summary_data['month'],
                    year=summary_data['year']
                ))
        
        logger.info(f"Total ringkasan yang diproses: {len(saved_insights)}")
        logger.info(f"Total summary awal: {len(all_summaries)}")
        logger.info(f"Total summary setelah filtering (hanya penurunan >10%): {len(filtered_summaries)}")
        
        return SummaryInsightResponse(
            data=saved_insights
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Error umum dalam get_scheduled_insight: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Terjadi kesalahan server internal: {str(e)}"
        )

# @router.get("/latest", response_model=SummaryInsightResponse)
# async def get_latest_scheduler_data():
#     """
#     Endpoint untuk mendapatkan data scheduler terbaru dari semua produk dari database.
    
#     Returns:
#         SummaryInsightResponse: Data scheduler terbaru dari semua produk
#     """
#     try:
#         async with db.async_session() as session:
#             latest_insights = []
#             all_summary_text = ''
            
#             for product_type in model_map.keys():
#                 query = select(SummaryInsight).where(
#                     SummaryInsight.product_type == product_type
#                 ).order_by(desc(SummaryInsight.created_at)).limit(1)
                
#                 result = await session.execute(query)
#                 insight = result.scalar_one_or_none()
                
#                 if insight:
#                     latest_insights.append(SummaryInsightItem(
#                         summary_text=insight.summary_text,
#                         insight=insight.insight,
#                         product_type=insight.product_type,
#                         month=insight.month,
#                         year=insight.year,
#                         created_at=insight.created_at
#                     ))
#                     all_summary_text += f"{product_type}: {insight.summary_text}\n"
#                     logger.info(f"Data terbaru ditemukan untuk {product_type}")
#                 else:
#                     logger.info(f"Tidak ada data untuk {product_type}")
            
#             if not latest_insights:
#                 raise HTTPException(
#                     status_code=status.HTTP_404_NOT_FOUND,
#                     detail="Tidak ada data scheduler yang ditemukan"
#                 )
                
#             endpoint = CHATBOT_ENDPOINT
#             headers = {
#                 "Content-Type": "application/json",
#                 "Accept": "text/html,application/json"
#             }
            
#             sanitized_message = ChatbotService.sanitize_input(all_summary_text)
#             data = {
#                 "user_query": sanitized_message,
#             }

#             logger.info(f"📤 Sending request to chatbot API: {endpoint}")

#             try:
#                 async with httpx.AsyncClient() as client:
#                     response = await client.post(
#                         endpoint,
#                         json=data,
#                         headers=headers,
#                         timeout=150.0
#                     )
#                     logger.info(f"📬 Response Status: {response.status_code}")
#                     logger.debug(f"Response Headers: {dict(response.headers)}")
#                     logger.debug(f"Response Content-Type: {response.headers.get('content-type', 'Not specified')}")
#                     response.raise_for_status()
                    
#                     # Tambahkan insight dari chatbot ke latest_insights
#                     try:
#                         response_data = response.json()
#                         chatbot_output = response_data.get('data', {}).get('output', '')
#                     except Exception as e:
#                         logger.warning(f"Response bukan JSON, menggunakan response text: {str(e)}")
#                         chatbot_output = response.text
                    
#                     chatbot_insight = SummaryInsightItem(
#                         summary_text="Chatbot Analysis",
#                         insight=chatbot_output,
#                         product_type="ALL",
#                         service_type="ALL",
#                         month="ALL",
#                         year="ALL",
#                         created_at=datetime.now()
#                     )
#                     latest_insights.append(chatbot_insight)
                    
#                     return SummaryInsightResponse(
#                         data=latest_insights
#                     )

#             except httpx.TimeoutException:
#                 logger.error("⏰ TIMEOUT ERROR - Request ke chatbot API timeout setelah 50 detik")
#                 raise HTTPException(
#                     status_code=504,
#                     detail="Request timeout - Chatbot API tidak merespons dalam waktu yang ditentukan"
#                 )
            
#     except HTTPException as e:
#         raise e
#     except Exception as e:
#         logger.error(f"Error dalam get_latest_scheduler_data: {str(e)}", exc_info=True)
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail=f"Terjadi kesalahan server internal: {str(e)}"
#         ) 
