from sqlalchemy import Column, String, Integer, Float, DateTime, Boolean, select, text, func, and_, Numeric, Index
from core.database import Base, db
import logging

logger = logging.getLogger(__name__)

class OrderSdwanService(Base):
    __tablename__ = "dbv_ebis_v_sdwan_service"
    __table_args__ = (
        Index('idx_sdwan_service_periode', 'periode_nf_aktif'),
        Index('idx_sdwan_service_divisi', 'divisi'),
        {'schema': 'satgas_ai'}
    )

    __additional_tablename__ = "dbv_wins_v_sdwan_service"

    periode_nf_aktif = Column(String, primary_key=True)
    divisi = Column(String)
    segmen = Column(String)
    product_group = Column(String)
    product = Column(String)
    product_label = Column(String)
    order_id = Column(String)
    value_bw = Column(Float)
    order_subtype = Column(String)
    revenue = Column(Numeric(38, 20))
    tarif_total = Column(Float)
    hjm = Column(Float)
    sdwan_topology = Column(String)
    sdwan_pkg = Column(String)
    edge_category = Column(String)
    edge_type = Column(String)
    flag_otc = Column(String)
    otc_seq_flg = Column(String)
    revenue_otc = Column(Numeric(38, 20))
    tarif_otc = Column(Numeric(38, 20))
    hjm_otc = Column(Numeric(38, 20))
    created_at = Column(DateTime)
    updated_at = Column(DateTime)

    @classmethod
    async def get_data_with_fallback(cls, session, query, year: int):
        """Scan data dari kedua tabel dan gabungkan hasilnya"""
        try:
            all_data = []
            
            # Scan dari tabel utama (ebis)
            logger.info(f"Scanning data dari {cls.__tablename__}")
            result = await session.execute(query)
            ebis_data = result.all()
            if ebis_data:
                logger.info(f"Ditemukan {len(ebis_data)} data dari {cls.__tablename__}")
                all_data.extend(ebis_data)
            
            # Scan dari tabel tambahan (wins)
            logger.info(f"Scanning data dari {cls.__additional_tablename__}")
            
            # Ambil query string dan parameter dari query asli
            compiled_query = query.compile(compile_kwargs={"literal_binds": True})
            query_str = str(compiled_query)
            params = compiled_query.params
            
            # Ganti nama tabel dalam query string
            additional_query_str = query_str.replace(
                f"{cls.__table_args__[2]['schema']}.{cls.__tablename__}", 
                f"{cls.__table_args__[2]['schema']}.{cls.__additional_tablename__}"
            )
            
            # Buat query baru menggunakan text()
            additional_query = text(additional_query_str)
            
            # Jalankan query dengan parameter yang sama
            result = await session.execute(additional_query, params)
            wins_data = result.all()
            if wins_data:
                logger.info(f"Ditemukan {len(wins_data)} data dari {cls.__additional_tablename__}")
                all_data.extend(wins_data)
            
            # Log total data yang ditemukan
            total_data = len(all_data)
            if total_data > 0:
                logger.info(f"Total data yang ditemukan dari kedua tabel: {total_data}")
            else:
                logger.warning("Tidak ada data yang ditemukan dari kedua tabel")
            
            return all_data
            
        except Exception as e:
            logger.error(f"Error dalam get_data_with_fallback: {str(e)}", exc_info=True)
            raise 