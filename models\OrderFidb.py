from sqlalchemy import Column, String, Integer, Float, DateTime, Boolean, select, text, func, and_, or_, Numeric, Index, extract, case
from core.database import Base, db
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class OrderFidb(Base):
    __tablename__ = "v_gla_all"
    __table_args__ = (
        Index('idx_fidb_periode', 'periode'),
        Index('idx_fidb_periode_service', 'periode', 'gl_dss_prod_cat'),
        Index('idx_fidb_divisi', 'divisi'),
        {'schema': 'satgas_ai'}
    )

    # Column definitions
    gl_dss_prod_grp = Column(String)
    gl_dss_prod_cat = Column(String)
    gl_account = Column(String)
    gl_desc = Column(String)
    kode_cc = Column(String)
    cfu = Column(String)
    divisi = Column(String)
    ubis = Column(String)
    segmen = Column(String)
    segmen_desc = Column(String)
    subsegmen_desc = Column(String)
    regional = Column(String)
    kode_regional = Column(String)
    witel = Column(String)
    witel_name = Column(String)
    tahun = Column(String)
    periode = Column(String, primary_key=True)
    bulan = Column(String)
    periode_rev = Column(DateTime)
    real_revenue = Column(Numeric(38, 20))
    target_revenue = Column(Numeric(38, 20))
    tgl_upd = Column(DateTime)

    # Definisikan kamus pemetaan service_type untuk scheduler
    _service_type_mapping = {
        "HSI B2B": ["HSI B2B"],
        "ASTINET": ["ASTINET"],
        "IP TRANSIT": ["IP TRANSIT"],
        "METRO ETHERNET": ["METRO ETHERNET"],
        "SL DOMESTIK": ["SL DOMESTIK"],
        "GLOBAL LINK": ["GLOBAL LINK"],
        "VPN IP": ["VPN IP"],
        "MANAGED SDWAN SERVICE": ["MANAGED SDWAN SERVICE"],
        "COLLOCATION DC": ["COLLOCATION DC"],
        "WIFI MANAGED SERVICE": ["WIFI MANAGED SERVICE"],
        "WIFI VOUCHER B2B2C": ["WIFI VOUCHER B2B2C"],
        "HSI B2C": ["HIGH SPEED INTERNET"],
        "INTERCONNECTION": [
            "Interconnection-Interconnection-Lapeks",
            "Interconnection-Interconnection-Non"
        ],
        "SMS A2P": ["SMS A2P-SMS A2P-Lapeks"],
        "VOICE-POTS": [
            "Voice-Call to Domestic OLO-Lapeks",
            "Voice-Call to domestic OLO-Lapeks",
            "Voice-Call to Domestic OLO-Non",
            "Voice-Voice POTS-Lapeks",
            "Voice-Voice POTS-Non",
            "Value Added IMS App Service-Voice POTS-Lapeks"
        ],
        "SIP TRUNK": [
            "Voice-SIP Trunk-Lapeks",
            "Voice-SIP Trunk-Non"
        ],
        "CALL CENTER": [
            "Call Center-Call Center-Lapeks",
            "Call Center-Call Center-Non"
        ],
        "WIFI VAS & ADD ON OTHERS CONNECTIVITY": ["WIFI DIGITAL ADVERTISING"],
        "WIFI WHOLESALE ISP": ["INTERNATIONAL ROAMING"]
    }

    @classmethod
    def get_service_type_expression(cls):
        """
        Membuat ekspresi SQL CASE untuk memetakan gl_dss_prod_cat ke service_type yang digabungkan.
        Jika tidak ada dalam pemetaan, nilai asli akan digunakan.
        """
        raw_service_col = getattr(cls, "gl_dss_prod_cat")
        # Balikkan kamus untuk mempermudah pencarian: {raw_value: new_value}
        reverse_mapping = {}
        for new_name, raw_list in cls._service_type_mapping.items():
            for raw_name in raw_list:
                reverse_mapping[raw_name] = new_name
        return case(
            reverse_mapping,
            value=raw_service_col,
            else_=raw_service_col
        ) 