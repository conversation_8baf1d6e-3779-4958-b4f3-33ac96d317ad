from sqlalchemy import Column, String, Integer, Float, DateTime, Boolean, select, text, func, and_, Numeric, Index
from core.database import Base, db
import logging

logger = logging.getLogger(__name__)

class OrderGlobalLink(Base):
    __tablename__ = "dbv_ebis_v_amdes_nf_aktif_globallink_final_xcl"
    __table_args__ = (
        Index('idx_globallink_period_date', 'period_date'),
        {'schema': 'satgas_ai'}
    )

    __additional_tablename__ = "dbv_wins_v_amdes_nf_aktif_globallink_final_xcl"

    billcomdate = Column(String)
    install = Column(Float)
    abonemen = Column(Float)
    revenue = Column(Numeric(38, 20))
    circuit_id = Column(String)
    circuit_name = Column(String)
    circuit_speed = Column(String)
    value_bw_mbps = Column(Float)
    product_name = Column(String)
    product_seq = Column(String)
    status_reason_txt = Column(String)
    effective_dtm = Column(DateTime)
    tariff_name = Column(String)
    ppn = Column(Float)
    tgl_suspend = Column(DateTime)
    update_dat = Column(DateTime)
    suspend = Column(String)
    service_witel = Column(String)
    tarif_total = Column(Float)
    hjm = Column(Float)
    rev_hjm = Column(Float)
    rev_hjm_percent = Column(Float)
    kat_hjm = Column(String)
    period_date = Column(DateTime, primary_key=True)
    created_at = Column(DateTime)
    updated_at = Column(DateTime)

    @classmethod
    async def get_data_with_fallback(cls, session, query, year: int):
        """Scan data dari kedua tabel dan gabungkan hasilnya"""
        try:
            all_data = []
            
            # Scan dari tabel utama (ebis)
            logger.info(f"Scanning data dari {cls.__tablename__}")
            result = await session.execute(query)
            ebis_data = result.all()
            if ebis_data:
                logger.info(f"Ditemukan {len(ebis_data)} data dari {cls.__tablename__}")
                all_data.extend(ebis_data)
            
            # Scan dari tabel tambahan (wins)
            logger.info(f"Scanning data dari {cls.__additional_tablename__}")
            
            # Ambil query string dan parameter dari query asli
            compiled_query = query.compile(compile_kwargs={"literal_binds": True})
            query_str = str(compiled_query)
            params = compiled_query.params
            
            # Ganti nama tabel dalam query string
            additional_query_str = query_str.replace(
                f"{cls.__table_args__[1]['schema']}.{cls.__tablename__}", 
                f"{cls.__table_args__[1]['schema']}.{cls.__additional_tablename__}"
            )
            
            # Buat query baru menggunakan text()
            additional_query = text(additional_query_str)
            
            # Jalankan query dengan parameter yang sama
            result = await session.execute(additional_query, params)
            wins_data = result.all()
            if wins_data:
                logger.info(f"Ditemukan {len(wins_data)} data dari {cls.__additional_tablename__}")
                all_data.extend(wins_data)
            
            # Log total data yang ditemukan
            total_data = len(all_data)
            if total_data > 0:
                logger.info(f"Total data yang ditemukan dari kedua tabel: {total_data}")
            else:
                logger.warning("Tidak ada data yang ditemukan dari kedua tabel")
            
            return all_data
            
        except Exception as e:
            logger.error(f"Error dalam get_data_with_fallback: {str(e)}", exc_info=True)
            raise 