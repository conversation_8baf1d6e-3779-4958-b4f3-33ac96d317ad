# import pandas as pd

# def total_revenue_between(
#     df,
#     target,
#     date_column,
#     start_year,
#     start_month,
#     end_year,
#     end_month,
#     division=None,
#     division_column=None,
#     service_type=None,
#     service_column=None
# ):
#     df = df.copy()

#     # Validasi kolom wajib
#     if date_column not in df.columns:
#         raise ValueError(f"Kolom '{date_column}' tidak ditemukan.")
#     if target not in df.columns:
#         raise ValueError(f"Kolom '{target}' tidak ditemukan.")

#     # Validasi kolom opsional (jika digunakan)
#     if division is not None and division_column not in df.columns:
#         raise ValueError(f"Kolom division '{division_column}' tidak ditemukan.")
#     if service_type is not None and service_column not in df.columns:
#         raise ValueError(f"Kolom service '{service_column}' tidak ditemukan.")

#     df = df.copy() 
#     try:
#         parsed_dates = pd.to_datetime(df[date_column], format='%Y%m', errors='coerce')

#         if parsed_dates.notna().sum() > 0:
#             df['year'] = parsed_dates.dt.year
#             df['month'] = parsed_dates.dt.month
#         else:
#             raise ValueError("Format YYYYMM tidak cocok")

#     except (ValueError, TypeError):
#         parsed_dates = pd.to_datetime(df[date_column], errors='coerce')
#         df['year'] = parsed_dates.dt.year
#         df['month'] = parsed_dates.dt.month

#     # Buat filter periode
#     start_period = int(f"{start_year}{start_month:02d}")
#     end_period = int(f"{end_year}{end_month:02d}")
#     df['periode_int'] = df['year'] * 100 + df['month']
#     filtered_df = df[(df['periode_int'] >= start_period) & (df['periode_int'] <= end_period)]

#     # Tambahkan filter divisi jika diberikan
#     if division is not None and division_column is not None:
#         filtered_df = filtered_df[filtered_df[division_column] == division]

#     # Tambahkan filter service type jika diberikan
#     if service_type is not None and service_column is not None:
#         filtered_df = filtered_df[filtered_df[service_column] == service_type]

#     # Hitung total revenue
#     total = filtered_df[target].sum()

#     return total
def total_revenue_between(
    df,
    target,
    date_column,
    start_year,
    start_month,
    end_year,
    end_month,
    year_column = None,
    month_column = None,
    division = None,
    division_column = None,
    service_type = None,
    service_column = None,
):
    df = df.copy()
    # Validasi kolom target
    if target not in df.columns:
        raise ValueError(f"Kolom '{target}' tidak ditemukan.")
    # Validasi input periode - harus ada salah satu
    if date_column is None and (year_column is None or month_column is None):
        raise ValueError("Harus menyediakan date_column ATAU year_column dan month_column")
    if date_column is not None and (year_column is not None or month_column is not None):
        raise ValueError("Gunakan date_column ATAU year_column+month_column, tidak keduanya")
    # Validasi kolom opsional (jika digunakan)
    if division is not None and division_column not in df.columns:
        raise ValueError(f"Kolom division '{division_column}' tidak ditemukan.")
    if service_type is not None and service_column not in df.columns:
        raise ValueError(f"Kolom service '{service_column}' tidak ditemukan.")
    # Mapping nama bulan ke angka dan sebaliknya
    month_mapping = {
        'JAN': 1, 'JANUARY': 1, 'januari': 1, 'jan': 1,
        'FEB': 2, 'FEBRUARY': 2, 'februari': 2, 'feb': 2,
        'MAR': 3, 'MARCH': 3, 'maret': 3, 'mar': 3,
        'APR': 4, 'APRIL': 4, 'april': 4, 'apr': 4,
        'MAY': 5, 'mei': 5, 'may': 5,
        'JUN': 6, 'JUNE': 6, 'juni': 6, 'jun': 6,
        'JUL': 7, 'JULY': 7, 'juli': 7, 'jul': 7,
        'AUG': 8, 'AUGUST': 8, 'agustus': 8, 'aug': 8,
        'SEP': 9, 'SEPTEMBER': 9, 'september': 9, 'sep': 9,
        'OCT': 10, 'OCTOBER': 10, 'oktober': 10, 'oct': 10,
        'NOV': 11, 'NOVEMBER': 11, 'november': 11, 'nov': 11,
        'DEC': 12, 'DECEMBER': 12, 'desember': 12, 'dec': 12
    }
    # Mapping angka ke nama bulan
    month_names = {
        1: 'Jan', 2: 'Feb', 3: 'Mar', 4: 'Apr', 5: 'May', 6: 'Jun',
        7: 'Jul', 8: 'Aug', 9: 'Sep', 10: 'Oct', 11: 'Nov', 12: 'Dec'
    }
    # Ekstrak tahun dan bulan berdasarkan input
    if date_column is not None:
        # Format periode YYYYMM
        if date_column not in df.columns:
            raise ValueError(f"Kolom '{date_column}' tidak ditemukan.")
        df[date_column] = df[date_column].astype(str)
        df['year'] = df[date_column].str[:4].astype(int)
        df['month'] = df[date_column].str[4:6].astype(int)
    else:
        # Format terpisah: kolom tahun dan bulan
        if year_column not in df.columns:
            raise ValueError(f"Kolom tahun '{year_column}' tidak ditemukan.")
        if month_column not in df.columns:
            raise ValueError(f"Kolom bulan '{month_column}' tidak ditemukan.")
        df['year'] = df[year_column].astype(int)
        # Cek apakah bulan berupa teks atau angka
        sample_month = str(df[month_column].iloc[0]).strip()
        if sample_month.upper() in month_mapping or sample_month.lower() in month_mapping:
            # Bulan dalam format teks
            df['month'] = df[month_column].astype(str).str.strip().map(
                lambda x: month_mapping.get(x.upper(), month_mapping.get(x.lower(), x))
            )
            # Cek jika ada bulan yang tidak bisa dimapping
            invalid_months = df[df['month'].apply(lambda x: not isinstance(x, int))]['month'].unique()
            if len(invalid_months) > 0:
                raise ValueError(f"Format bulan tidak dikenali: {invalid_months}")
        else:
            # Bulan dalam format angka
            df['month'] = df[month_column].astype(int)
    # Buat filter periode
    start_period = int(f"{start_year}{start_month:02d}")
    end_period = int(f"{end_year}{end_month:02d}")
    df['periode_int'] = df['year'] * 100 + df['month']
    filtered_df = df[(df['periode_int'] >= start_period) & (df['periode_int'] <= end_period)]
    # Tambahkan filter divisi jika diberikan
    if division is not None and division_column is not None:
        filtered_df = filtered_df[filtered_df[division_column] == division]
    # Tambahkan filter service type jika diberikan
    if service_type is not None and service_column is not None:
        filtered_df = filtered_df[filtered_df[service_column] == service_type]
    # Hitung total revenue
    total = filtered_df[target].sum()
    # Format output
    def format_revenue(amount):
        if amount >= 1_000_000_000:
            return f"{amount/1_000_000_000:.1f}M"
        elif amount >= 1_000_000:
            return f"{amount/1_000_000:.1f}jt"
        elif amount >= 1_000:
            return f"{amount/1_000:.1f}rb"
        else:
            return f"{amount:.0f}"
    
    # Hitung revenue per bulan
    monthly_revenue = filtered_df.groupby(['year', 'month'])[target].sum().reset_index()
    monthly_revenue = monthly_revenue.sort_values(['year', 'month'])
    # Print total revenue
    start_month_name = month_names[start_month]
    end_month_name = month_names[end_month]
    lines = [f"Total revenue {start_month_name} {start_year} - {end_month_name} {end_year}: {format_revenue(total)}"]
    for _, row in monthly_revenue.iterrows():
        year = int(row['year'])
        month = int(row['month'])
        revenue = row[target]
        month_name = month_names[month]
        lines.append(f"Revenue {month_name} {year}: {format_revenue(revenue)}")

    return "\n".join(lines)