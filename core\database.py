from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker, declarative_base
from settings import (
    DB_USER,
    DB_PASS,
    DB_HOST,
    DB_PORT,
    DB_NAME
)
import os

# Base class untuk model
Base = declarative_base()

class DatabaseConnection:
    _instance = None
    _engine = None
    _async_session = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DatabaseConnection, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if self._engine is None:
            self._setup_connection()

    def _setup_connection(self):
        try:
            host = DB_HOST
            port = DB_PORT

            # Buat async engine
            DATABASE_URL = f"postgresql+asyncpg://{DB_USER}:{DB_PASS}@{host}:{port}/{DB_NAME}"
            self._engine = create_async_engine(
                DATABASE_URL,
                echo=False,
                future=True
            )

            # Buat async session factory
            self._async_session = sessionmaker(
                self._engine,
                class_=AsyncSession,
                expire_on_commit=False
            )

        except Exception as e:
            print(f"Error setting up database connection: {str(e)}")
            raise

    @property
    def async_session(self):
        if self._async_session is None:
            self._setup_connection()
        return self._async_session

    async def close(self):
        if self._engine is not None:
            await self._engine.dispose()

# Singleton instance
db = DatabaseConnection()
async_session = db.async_session 