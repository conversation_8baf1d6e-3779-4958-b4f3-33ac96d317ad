from sqlalchemy import Column, String, Integer, Float, DateTime, Boolean, select, text, func, and_, Numeric, Index
from core.database import Base, db
import logging

logger = logging.getLogger(__name__)

class OrderNeuCentrixAbonemen(Base):
    __tablename__ = "dbv_ebis_v_amdes_nf_aktif_cndc_final"
    __table_args__ = (
        Index('idx_neucentrix_abonemen_period', 'period_date'),
        {'schema': 'satgas_ai'}
    )

    __additional_tablename__ = "dbv_wins_v_amdes_nf_aktif_cndc_final"

    periode = Column(String)
    period_date = Column(DateTime, primary_key=True)
    customer_ref = Column(String)
    account_num = Column(String)
    account_name = Column(String)
    business_share = Column(Float)
    divisi = Column(String)
    segmen = Column(String)
    currency_code = Column(String)
    product_label = Column(String)
    cust_order_num = Column(String)
    install = Column(String)
    abonemen = Column(Numeric(38, 20))
    circuit_id = Column(String)
    circuit_name = Column(String)
    circuit_speed = Column(String)
    product_name = Column(String)
    product_group = Column(String)
    product_seq = Column(Integer)
    status_reason_txt = Column(String)
    effective_dtm = Column(DateTime)
    tariff_name = Column(String)
    ppn = Column(Float)
    tgl_suspend = Column(DateTime)
    update_dat = Column(DateTime)
    sto_code = Column(String)
    lokasi = Column(String)
    pkg_data_cntr = Column(Integer)
    jml_pkg_data_cntr = Column(Integer)
    pkg_booking = Column(Integer)
    jml_pkg_booking = Column(Integer)
    tarif_pkg = Column(Float)
    jml_upgrade_ps_single_mrc = Column(Integer)
    tarif_ps_single = Column(Float)
    jml_upgrade_ps_dual_mrc = Column(Integer)
    tarif_ps_dual = Column(Float)
    jml_sewa_handhole = Column(Integer)
    tarif_sewa_handhole = Column(Float)
    membership = Column(String)
    tarif_membership = Column(Float)
    jml_add_cross_cnnct_utp = Column(Integer)
    tarif_cnnct_utp = Column(Float)
    jml_add_cross_cnnct_fiber = Column(Integer)
    tarif_cnnct_fiber = Column(Float)
    jml_port_mlpa_blpa_100mb = Column(Integer)
    tarif_port_100mb = Column(Float)
    jml_port_mlpa_blpa_1gb = Column(Integer)
    tarif_port_1gb = Column(Float)
    jml_port_mlpa_blpa_10gb = Column(Integer)
    tarif_port_10gb = Column(Float)
    jml_vlan = Column(Integer)
    tarif_total = Column(Float)
    hjm = Column(Float)
    custaccntname = Column(String)
    sto = Column(String)
    sto_name_cndc = Column(String)
    pkg_data_cntr_1 = Column(Integer)
    nip_nas = Column(String)
    lokasi_asal = Column(String)
    lokasi_tujuan = Column(String)
    port_qty = Column(Integer)
    bw_gbps = Column(Float)
    tarif_xconnect = Column(Float)
    order_subtype = Column(String)
    tier = Column(String)
    jml_sewa_jalur_kbl_transmisi = Column(Integer)
    tarif_sewa_kbl_transmisi = Column(Float)
    li_status_date = Column(DateTime)
    nip_nas_cbase = Column(String)
    standard_name_cbase = Column(String)
    segmen_cbase = Column(String)
    service_witel = Column(String)
    service_region = Column(String)
    remote_ncix = Column(String)
    is_ao_period = Column(Boolean)
    precabling_utp = Column(String)
    precabling_fiber = Column(String)
    bill_mny = Column(Float)
    cx_termin_value = Column(Float)
    cx_termin_flg = Column(Boolean)
    termin_seq_flg = Column(Boolean)
    cx_otc_amount = Column(Float)
    termin_hjm = Column(Float)
    termin_tarif = Column(Float)
    cntrc_duration = Column(Integer)
    jml_sequence = Column(Integer)
    revenue_final = Column(Float)
    hjm_final = Column(Float)
    tarif_final = Column(Float)
    flag_otc = Column(Boolean)
    otc_seq_flg = Column(Boolean)
    revenue_otc = Column(Float)
    tarif_otc = Column(Float)
    hjm_otc = Column(Float)
    term_payment = Column(String)
    revenue_mrc = Column(Float)
    tarif_mrc = Column(Float)
    hjm_mrc = Column(Float)
    mrc_seq_flg = Column(Boolean)
    flag_top = Column(Boolean)
    kat_ps = Column(String)
    reg_new = Column(String)
    witel_new = Column(String)
    longitude = Column(Float)
    latitude = Column(Float)

    @classmethod
    async def get_data_with_fallback(cls, session, query, year: int):
        """Scan data dari kedua tabel dan gabungkan hasilnya"""
        try:
            all_data = []
            
            # Scan dari tabel utama (ebis)
            logger.info(f"Scanning data dari {cls.__tablename__}")
            result = await session.execute(query)
            ebis_data = result.all()
            if ebis_data:
                logger.info(f"Ditemukan {len(ebis_data)} data dari {cls.__tablename__}")
                all_data.extend(ebis_data)
            
            # Scan dari tabel tambahan (wins)
            logger.info(f"Scanning data dari {cls.__additional_tablename__}")
            
            # Ambil query string dan parameter dari query asli
            compiled_query = query.compile(compile_kwargs={"literal_binds": True})
            query_str = str(compiled_query)
            params = compiled_query.params
            
            # Ganti nama tabel dalam query string
            additional_query_str = query_str.replace(
                f"{cls.__table_args__[1]['schema']}.{cls.__tablename__}", 
                f"{cls.__table_args__[1]['schema']}.{cls.__additional_tablename__}"
            )
            
            # Buat query baru menggunakan text()
            additional_query = text(additional_query_str)
            
            # Jalankan query dengan parameter yang sama
            result = await session.execute(additional_query, params)
            wins_data = result.all()
            if wins_data:
                logger.info(f"Ditemukan {len(wins_data)} data dari {cls.__additional_tablename__}")
                all_data.extend(wins_data)
            
            # Log total data yang ditemukan
            total_data = len(all_data)
            if total_data > 0:
                logger.info(f"Total data yang ditemukan dari kedua tabel: {total_data}")
            else:
                logger.warning("Tidak ada data yang ditemukan dari kedua tabel")
            
            return all_data
            
        except Exception as e:
            logger.error(f"Error dalam get_data_with_fallback: {str(e)}", exc_info=True)
            raise 