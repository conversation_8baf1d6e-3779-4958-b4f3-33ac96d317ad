import asyncio
import pytest
from sqlalchemy import select, cast, Numeric, func, text

from core.database import db
from models.OrderChurnDatacomm import OrderChurnDatacomm


@pytest.mark.asyncio
async def test_churn_datacomm_cndc_count_matches_manual_query():
    """
    Validate that the count of revenue rows for CNDC at periode '202504' across
    ebis + wins matches the provided manual SQL (UNION ALL + CAST).
    """
    target_product = 'CNDC'
    target_periode = '202504'

    # 1) Run the provided manual SQL directly
    manual_sql = text(
        """
        SELECT COUNT(total_revenue) AS total_rupiah
        FROM (
            SELECT CAST(revenue AS NUMERIC) AS total_revenue
            FROM satgas_ai.dbv_ebis_mart_order_datacomm
            WHERE product = :product AND periode = :periode
            UNION ALL
            SELECT CAST(revenue AS NUMERIC) AS total_revenue
            FROM satgas_ai.dbv_wins_mart_order_datacomm
            WHERE product = :product AND periode = :periode
        ) AS combined;
        """
    )

    async with db.async_session() as session:
        manual_result = await session.execute(manual_sql, {"product": target_product, "periode": target_periode})
        manual_count = manual_result.scalar_one()

        # 2) Compute the same count via ORM for both tables and sum
        #    First: EBIS (primary) table via the mapped model
        ebis_count_query = select(func.count()).select_from(OrderChurnDatacomm).where(
            OrderChurnDatacomm.product == target_product,
            OrderChurnDatacomm.periode == target_periode,
            cast(OrderChurnDatacomm.revenue, Numeric) != None  # ensure castable/non-null
        )
        ebis_count = (await session.execute(ebis_count_query)).scalar_one()

        # 3) WINS (additional) table by compiling the ORM query then swapping table name
        compiled = ebis_count_query.compile(compile_kwargs={"literal_binds": True})
        ebis_sql = str(compiled)
        wins_sql = ebis_sql.replace(
            f"{OrderChurnDatacomm.__table_args__[1]['schema']}.{OrderChurnDatacomm.__tablename__}",
            f"{OrderChurnDatacomm.__table_args__[1]['schema']}.{OrderChurnDatacomm.__additional_tablename__}"
        )
        wins_count = (await session.execute(text(wins_sql))).scalar_one()

        orm_total_count = (ebis_count or 0) + (wins_count or 0)

        # Assert equality with manual SQL count
        assert orm_total_count == manual_count, (
            f"Mismatch count for CNDC {target_periode}: ORM={orm_total_count}, MANUAL={manual_count}"
        )


