import pandas as pd
import numpy as np


def compare(df, target, year1, month1, year2, month2, date_column, service_column=None, service_type=None, product_type=None):
    df = df.copy()
    
    if date_column not in df.columns:
        raise ValueError(f"Kolom '{date_column}' tidak ditemukan dalam data.")
    if target not in df.columns:
        raise ValueError(f"Kolom '{target}' tidak ditemukan dalam data.")
    if service_type is not None and service_column not in df.columns:
        raise ValueError(f"Kolom '{service_column}' tidak ditemukan dalam data.")

    df = df.copy() 
    try:
        parsed_dates = pd.to_datetime(df[date_column], format='%Y%m', errors='coerce')

        if parsed_dates.notna().sum() > 0:
            df['year'] = parsed_dates.dt.year
            df['month'] = parsed_dates.dt.month
        else:
            raise ValueError("Format YYYYMM tidak cocok")

    except (ValueError, TypeError):
        parsed_dates = pd.to_datetime(df[date_column], errors='coerce')
        df['year'] = parsed_dates.dt.year
        df['month'] = parsed_dates.dt.month

    df.dropna(subset=['year', 'month'], inplace=True)
    df[['year', 'month']] = df[['year', 'month']].astype(int)

    # Filter tahun & bulan
    df1 = df[(df['year'] == year1) & (df['month'] == month1)]
    df2 = df[(df['year'] == year2) & (df['month'] == month2)]

    # Kalau service_type disediakan → filter berdasarkan kolom tersebut
    if service_type is not None:
        df1 = df1[df1[service_column] == service_type]
        df2 = df2[df2[service_column] == service_type]

    total1 = df1[target].sum()
    total2 = df2[target].sum()

    def format_diff(curr, prev):
        if prev == 0:
            return "tidak dapat dihitung (data sebelumnya nol)", "stabil", 0, 0
        diff = curr - prev
        pct = (diff / prev) * 100
        rupiah = f"sekitar Rp{abs(diff):,.0f}".replace(",", ".")
        arah = "kenaikan" if diff > 0 else "penurunan"
        return f"{arah} {target} sebesar {abs(pct):.1f}% ({rupiah})", arah, pct, diff

    result, _, _, _ = format_diff(total1, total2)
    month1_str = pd.to_datetime(f"{year1}-{month1:02}-01").strftime('%B %Y')
    month2_str = pd.to_datetime(f"{year2}-{month2:02}-01").strftime('%B %Y')

    # Tambahan teks jika pakai service_type
    service_text = f" untuk layanan {service_type}" if service_type else ""
    product_text = f" pada produk {product_type}" if product_type else ""
    summary = f"Dibandingkan antara {month2_str} dan {month1_str}, terjadi {result}{service_text}{product_text}."

    return {
        "compare_from": f"{month2_str}",
        "compare_to": f"{month1_str}",
        "target": target,
        "revenue_current": total1,
        "revenue_last": total2,
        "service_type": service_type,
        "product_type": product_type,
        "summary_text": summary
    }
