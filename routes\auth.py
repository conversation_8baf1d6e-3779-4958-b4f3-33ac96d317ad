
import traceback
from core.file import generate_link_download
from core.mail import send_reset_password_email
from fastapi import APIRouter, Depends, Request, BackgroundTasks, UploadFile, File, Form, Request
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from core.responses import (
    common_response,
    Ok,
    CudResponse,
    BadRequest,
    Unauthorized,
    NotFound,
    InternalServerError,
)
from models import get_db
from core.security import get_user_from_jwt_token, generate_jwt_token_from_user, get_user_permissions
from core.security import (
    get_user_from_jwt_token,
    oauth2_scheme,
)
from schemas.common import (
    BadRequestResponse,
    UnauthorizedResponse,
    NotFoundResponse,
    InternalServerErrorResponse,
    CudResponseSchema,
)
from schemas.auth import (
    ForgotPasswordChangePasswordRequest,
    ForgotPasswordChangePasswordResponse,
    ForgotPasswordSendEmailRequest,
    LoginSuccessResponse,
    LoginSuccess,
    LoginRequest,
    MeSuccessResponse,
    MenuResponse,
    PermissionsResponse,
    EditUserRequest,
    SignUpRequest,
    ForgotPasswordSendEmailResponse,
    RoleOptionsResponse,
)
import repository.auth  as authRepo
from urllib.parse import urlparse

router = APIRouter(tags=["Auth"])

@router.post("/token")
async def generate_token(
    db: AsyncSession = Depends(get_db),
    form_data: OAuth2PasswordRequestForm = Depends()
):
    try:
        is_valid =await authRepo.check_user_password(
            db, form_data.username, form_data.password
        )
        if not is_valid:
            return common_response(BadRequest(message="Invalid Credentials"))
        user = is_valid
        token = await generate_jwt_token_from_user(user=user)
        await authRepo.create_user_session(db=db, user_id=user.id, token=token)
        return {"access_token": token, "token_type": "Bearer"}
    except Exception as e:
        return common_response(BadRequest(message=str(e)))