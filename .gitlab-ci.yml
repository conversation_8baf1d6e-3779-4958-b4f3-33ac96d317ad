stages:
 - build
 - deploy

variables:
  DOCKER_DRIVER: overlay2
  CONTAINER_IMAGE_REPO: satgas-ai-pgm/machine-learning-dev
  CONTAINER_IMAGE: satgas-ai-pgm/machine-learning-dev:latest
  CONTAINER_RELEASE_IMAGE: harbor.telkom.co.id/satgas-ai-pgm/machine-learning-dev:latest
  CONTAINER_IMAGE_REPO_PRD: satgas-ai-pgm/machine-learning-prd
  CONTAINER_IMAGE_PRD: satgas-ai-pgm/machine-learning-prd:latest
  CONTAINER_RELEASE_IMAGE_PRD: harbor.telkom.co.id/satgas-ai-pgm/machine-learning-prd:latest
  DOCKER_TLS_CERTDIR: ""
  DOCKER_REPO : harbor.telkom.co.id
  USER_DOCKER : putro_sigma
  PROJECT_DEV: pgm-dashboard-dev
  PROJECT_PROD: dcs-dashboard-prod
  DEP_DEV2: machine-learning-dev
  DEP_PROD: machine-learning-prod

build-image:
  stage: build
  only:
    - development
  tags:
    - dit
    - docker
  script:
    - docker login $DOCKER_REPO --username $USER_DOCKER --password $PASSWORD_REPO
    - docker build -t $CONTAINER_IMAGE .
    - docker tag $CONTAINER_IMAGE_REPO $CONTAINER_RELEASE_IMAGE
    - docker push $CONTAINER_RELEASE_IMAGE
    
deploy-image:
  stage: deploy
  only:
    - development
  tags:
    - myihx2
  script:
    - echo "This for job rollout deploy"
    - oc login -u $OC_LOGIN -p $OC_PASS --server=$OPENSHIFT --insecure-skip-tls-verify
    - oc project $PROJECT_DEV
    - oc rollout restart deployment/$DEP_DEV2

build-image-prod:
  stage: build
  only:
    - production
  tags:
    - dit
    - docker
  script:
    - docker login $DOCKER_REPO --username $USER_DOCKER --password $PASSWORD_REPO
    - docker build -t $CONTAINER_IMAGE_PRD .
    - docker tag $CONTAINER_IMAGE_REPO_PRD $CONTAINER_RELEASE_IMAGE_PRD
    - docker push $CONTAINER_RELEASE_IMAGE_PRD
    
deploy-image-prod:
  stage: deploy
  only:
    - production
  tags:
    - myihx2
  script:
    - echo "This for job rollout deploy"
  #  - oc login --token=$OC_TOKEN_DEV --server=$OPENSHIFT --insecure-skip-tls-verify
    - oc login -u $OC_LOGIN_PRD -p $OC_PASS_PRD --server=$OPENSHIFT --insecure-skip-tls-verify
    - oc project $PROJECT_PROD
    - oc rollout restart deployment/$DEP_PROD