from sqlalchemy import Column, String, Integer, Float, DateTime, Boolean, select, text, func, and_, Numeric, BigInteger, Text, Index
from core.database import Base, db
import logging

logger = logging.getLogger(__name__)

class OrderSlwdm(Base):
    __tablename__ = "dbv_ebis_v_amdes_nf_aktif_slwdm_final"
    __table_args__ = (
        Index('idx_slwdm_periode', 'periode'),
        Index('idx_slwdm_periode_product', 'periode', 'product_name'),
        Index('idx_slwdm_divisi', 'divisi'),
        {'schema': 'satgas_ai'}
    )
    
    __additional_tablename__ = "dbv_wins_v_amdes_nf_aktif_slwdm_final"

    periode = Column(BigInteger, primary_key=True)
    customer_ref = Column(Float)
    account_num = Column(Float)
    account_name = Column(Text)
    business_share = Column(Float)
    divisi = Column(Text)
    segmen = Column(Float)
    currency_code = Column(Text)
    product_label = Column(Text)
    cust_order_num = Column(Text)
    install = Column(Float)
    abonemen = Column(Float)
    circuit_id = Column(Float)
    circuit_name = Column(Float)
    circuit_speed = Column(Float)
    value_speed_gbps = Column(Float)
    product_name = Column(Text)
    product_group = Column(Float)
    product_seq = Column(Float)
    status_reason_txt = Column(Float)
    effective_dtm = Column(Float)
    tariff_name = Column(Float)
    ppn = Column(Float)
    tgl_suspend = Column(Float)
    update_dat = Column(Float)
    sero_id = Column(Float)
    seoa_id = Column(Float)
    seoa_sero_id = Column(Float)
    seoa_sero_revision = Column(Float)
    seoa_display_order = Column(Float)
    seoa_prev_value = Column(Float)
    seoa_sofe_id = Column(Float)
    seoa_hidden = Column(Float)
    protection_a = Column(Float)
    protection_b = Column(Float)
    bandwidth = Column(Float)
    region_a = Column(Float)
    sto_a2 = Column(Float)
    region_b = Column(Float)
    sto_b2 = Column(Float)
    distance = Column(Float)
    shiptoparty = Column(Float)
    name1sold = Column(Float)
    name1ship1 = Column(Float)
    createdon = Column(Float)
    street1sold = Column(Float)
    notelp = Column(Float)
    treg = Column(Text)
    witel = Column(Float)
    billcomdate = Column(Text)
    cntrc_sdate = Column(Float)
    cntrc_edate = Column(Float)
    kota_asal = Column(Text)
    kota_tujuan = Column(Text)
    sl_digital_type = Column(Float)
    sl_digital_bandwidth = Column(Float)
    des_lokasi_sldigital = Column(Float)
    lokasi_sldigital = Column(Float)
    des_lokasi_telp = Column(Float)
    wdm_reg_asal = Column(Text)
    wdm_reg_tujuan = Column(Text)
    wdm_fix_speed_gbps = Column(Float)
    wdm_jarak_km = Column(Text)
    wdm_tipe_akses = Column(Float)
    name = Column(Text)
    wdm_speed = Column(Float)
    bandwidth_a = Column(Float)
    bandwidth_b = Column(Float)
    agree_name = Column(Float)
    service_witel = Column(Float)
    service_region = Column(Float)
    agree_start_date = Column(Float)
    agree_end_date = Column(Float)
    li_status_date = Column(Float)
    custaccntname = Column(Text)
    order_subtype = Column(Float)
    revenue = Column(Float)
    tarif_total = Column(Float)
    diskon_rp = Column(Float)
    diskon_percent = Column(Float)
    hjm = Column(Float)
    margin_rev_hjm_rp = Column(Float)
    margin_rev_hjm_percent = Column(Float)
    kat_hjm = Column(Text)
    suspend = Column(Text)
    billcomdate_1 = Column(Text)
    subsegment = Column(Float)
    nip_nas_cbase = Column(Float)
    standard_name_cbase = Column(Float)
    segmen_cbase = Column(Float)
    is_ao_period = Column(Text)
    bill_mny = Column(Float)
    cx_termin_value = Column(Float)
    cx_termin_flg = Column(Float)
    termin_seq_flg = Column(Float)
    cx_otc_amount = Column(Float)
    termin_hjm = Column(Float)
    termin_tarif = Column(Float)
    cntrc_duration = Column(Float)
    jml_sequence = Column(Float)
    revenue_final = Column(Float)
    hjm_final = Column(Float)
    tarif_final = Column(Float)
    flag_otc = Column(Float)
    otc_seq_flg = Column(Float)
    revenue_otc = Column(Float)
    tarif_otc = Column(Float)
    hjm_otc = Column(Float)
    term_payment = Column(Float)
    revenue_mrc = Column(Float)
    tarif_mrc = Column(Float)
    hjm_mrc = Column(Float)
    mrc_seq_flg = Column(Float)
    flag_top = Column(Float)
    reg_new = Column(Float)
    witel_new = Column(Float)
    sto_code = Column(Float)

    @classmethod
    async def get_data_with_fallback(cls, session, query, year: int):
        """Scan data dari kedua tabel dan gabungkan hasilnya"""
        try:
            all_data = []
            
            # Scan dari tabel utama (ebis)
            logger.info(f"Scanning data dari {cls.__tablename__}")
            result = await session.execute(query)
            ebis_data = result.all()
            if ebis_data:
                logger.info(f"Ditemukan {len(ebis_data)} data dari {cls.__tablename__}")
                all_data.extend(ebis_data)
            
            # Scan dari tabel tambahan (wins)
            logger.info(f"Scanning data dari {cls.__additional_tablename__}")
            
            # Ambil query string dan parameter dari query asli
            compiled_query = query.compile(compile_kwargs={"literal_binds": True})
            query_str = str(compiled_query)
            params = compiled_query.params
            
            # Ganti nama tabel dalam query string
            additional_query_str = query_str.replace(
                f"{cls.__table_args__[3]['schema']}.{cls.__tablename__}", 
                f"{cls.__table_args__[3]['schema']}.{cls.__additional_tablename__}"
            )
            
            # Buat query baru menggunakan text()
            additional_query = text(additional_query_str)
            
            # Jalankan query dengan parameter yang sama
            result = await session.execute(additional_query, params)
            wins_data = result.all()
            if wins_data:
                logger.info(f"Ditemukan {len(wins_data)} data dari {cls.__additional_tablename__}")
                all_data.extend(wins_data)
            
            # Log total data yang ditemukan
            total_data = len(all_data)
            if total_data > 0:
                logger.info(f"Total data yang ditemukan dari kedua tabel: {total_data}")
            else:
                logger.warning("Tidak ada data yang ditemukan dari kedua tabel")
            
            return all_data
            
        except Exception as e:
            logger.error(f"Error dalam get_data_with_fallback: {str(e)}", exc_info=True)
            raise 