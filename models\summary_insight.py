from sqlalchemy import Column, Integer, String, Text, Boolean, TIMESTAMP, func
from models import Base

class SummaryInsight(Base):
    __tablename__ = "summary_insight"
    __table_args__ = {'schema': 'dcsbe'}

    id = Column(Integer, primary_key=True, autoincrement=True, index=True)
    summary_text = Column(Text, nullable=False)
    insight = Column(Text, nullable=True)
    isdel = Column(Boolean, default=False)
    product_type = Column(String, nullable=False)
    service_type = Column(String, nullable=True)
    month = Column(String, nullable=False)
    year = Column(String, nullable=False)
    created_at = Column(TIMESTAMP, server_default=func.now()) 