"""
Product Configuration Mapping
Berdasarkan konfigurasi produk dan tabel database
"""

PRODUCT_CONFIG = {
    "sap": {
        "primary_table": "v_gla_all",
        "additional_tables": [],
        "revenue_column": "real_revenue",
        "date_column": "periode_rev",
        "service_column": "gl_dss_prod_cat",
        "lis_column": None,
        "bandwidth_column": None,
        "period_type": "datetime",
        "schema": "satgas_ai"
    },
    "voice_pots": {
        "primary_table": "dbv_ebis_v_osr_pots_hsi_summary",
        "additional_tables": [],
        "revenue_column": "revenue",
        "date_column": "periode_billing",
        "service_column": "produk",
        "lis_column": "jumlah_lis",
        "bandwidth_column": "mbps",
        "period_type": "datetime",
        "schema": "satgas_ai"
    },
    "vpn_ip": {
        "primary_table": "dbv_ebis_v_amdes_nf_aktif_vpnip_final",
        "additional_tables": ["dbv_wins_v_amdes_nf_aktif_vpnip_final"],
        "revenue_column": "revenue_final",
        "date_column": "periode",
        "service_column": "service_type_1",
        "lis_column": "product_label",
        "bandwidth_column": "value_speed_mbps",
        "period_type": "integer_yyyymm",
        "schema": "satgas_ai"
    },
    "metro_ethernet": {
        "primary_table": "dbv_ebis_v_amdes_nf_aktif_metroe_final",
        "additional_tables": ["dbv_wins_v_amdes_nf_aktif_metroe_final"],
        "revenue_column": "revenue_final",
        "date_column": "periode",
        "service_column": "service_type",
        "lis_column": "product_label",
        "bandwidth_column": "value_bw_mbps",
        "period_type": "integer_yyyymm",
        "schema": "satgas_ai"
    },
    "ip_transit": {
        "primary_table": "dbv_ebis_v_amdes_mart_order_iptransit_final",
        "additional_tables": ["dbv_wins_v_amdes_mart_order_iptransit_final"],
        "revenue_column": "revenue",
        "date_column": "periode",
        "service_column": "service_type",
        "lis_column": None,
        "bandwidth_column": "value_speed_mbps",
        "period_type": "integer_yyyymm",
        "schema": "satgas_ai"
    },
    "astinet": {
        "primary_table": "dbv_ebis_v_amdes_nf_aktif_astinet_finalx",
        "additional_tables": ["dbv_wins_v_amdes_nf_aktif_astinet_finalx"],
        "revenue_column": "revenue_final",
        "date_column": "periode",
        "service_column": "variant",
        "lis_column": "product_label",
        "bandwidth_column": "value_speed",
        "period_type": "integer_yyyymm",
        "schema": "satgas_ai"
    },
        "slwdm": {
        "primary_table": "dbv_ebis_v_amdes_nf_aktif_slwdm_final",
        "additional_tables": ["dbv_wins_v_amdes_nf_aktif_slwdm_final"],
        "revenue_column": "revenue",
        "date_column": "periode",
        "service_column": "name",
        "lis_column": "product_label",
        "bandwidth_column": "value_speed_gbps",
        "period_type": "bigint_yyyymm",  # Ubah ke bigint_yyyymm karena database bertipe BIGINT
        "schema": "satgas_ai"
    },
    "neucentrix_abonemen": {
        "primary_table": "dbv_ebis_v_amdes_nf_aktif_cndc_final",
        "additional_tables": ["dbv_wins_v_amdes_nf_aktif_cndc_final"],
        "revenue_column": "abonemen",
        "date_column": "period_date",
        "service_column": "product_group",
        "lis_column": "product_label",
        "bandwidth_column": None,
        "period_type": "datetime",  
        "schema": "satgas_ai",
        "date_offset": -1
    },
    "neucentrix_revenue": {
        "primary_table": "dbv_ebis_v_nf_aktif_cndc_pullthru",
        "additional_tables": ["dbv_wins_v_nf_aktif_cndc_pullthru"],
        "revenue_column": "revenue",
        "date_column": "period_date",
        "service_column": "product_group",
        "lis_column": "product_label",
        "bandwidth_column": "value_bw",
        "period_type": "datetime", 
        "schema": "satgas_ai",
        "date_offset": -1
    },
    "sl_domestik": {
        "primary_table": "dbv_ebis_v_amdes_nf_aktif_sl_digital_final",
        "additional_tables": ["dbv_wins_v_amdes_nf_aktif_sl_digital_final"],
        "revenue_column": "rupiah",
        "date_column": "periode",
        "service_column": "product_name",
        "lis_column": "product_label",
        "bandwidth_column": "value_speed_mbps",
        "period_type": "integer_yyyymm",
        "schema": "satgas_ai"
    },
    "global_link": {
        "primary_table": "dbv_ebis_v_amdes_nf_aktif_globallink_final_xcl",
        "additional_tables": ["dbv_wins_v_amdes_nf_aktif_globallink_final_xcl"],
        "revenue_column": "revenue",
        "date_column": "period_date",
        "service_column": "produk_label",
        "lis_column": None,
        "bandwidth_column": None,
        "period_type": "datetime", 
        "schema": "satgas_ai"
    },
    "sdwan_connectivity": {
        "primary_table": "dbv_ebis_v_sdwan_connectivity",
        "additional_tables": ["dbv_wins_v_sdwan_connectivity"],
        "revenue_column": "revenue",
        "date_column": "periode_nf_aktif",
        "service_column": "product", 
        "lis_column": None,
        "bandwidth_column": "value_bw",
        "period_type": "integer_yyyymm",
        "schema": "satgas_ai",
        "date_offset": -1
    },
    "sdwan_service": {
        "primary_table": "dbv_ebis_v_sdwan_service",
        "additional_tables": ["dbv_wins_v_sdwan_service"],
        "revenue_column": "revenue",
        "date_column": "periode_nf_aktif",
        "service_column": "product",  
        "lis_column": "periode", 
        "bandwidth_column": "value_bw",
        "period_type": "integer_yyyymm",
        "schema": "satgas_ai",
        "date_offset": -1
    },
    "hsi_b2b": {
        "primary_table": "dbv_ebis_hsi_b2b_profile_new",
        "additional_tables": [],
        "revenue_column": "trems_rev",
        "date_column": "period",
        "service_column": "citem",
        "lis_column": "notel",
        "bandwidth_column": None, #citem-prefix (HSIE300M)
        "period_type": "datetime",  
        "schema": "satgas_ai",
        "date_offset": -1
    },
    "order_hsi_pots": {
        "primary_table": "dbv_ebis_v_kpro_detail_order_ps",
        "additional_tables": [],
        "revenue_column": None,
        "date_column": None,
        "service_column": None,
        "lis_column": None,
        "bandwidth_column": None,
        "period_type": None,
        "schema": "satgas_ai"
    },
    "churn_datacomm": {
        "primary_table": "dbv_ebis_mart_order_datacomm",
        "additional_tables": ["dbv_wins_mart_order_datacomm"],
        "revenue_column": "revenue",
        "date_column": "periode",
        "service_column": "product",  # Ubah dari produk ke service_type
        "lis_column": 'sid',
        "bandwidth_column": "value_speed_mbps",
        "period_type": "integer_yyyymm",
        "schema": "satgas_ai"
    },
    "churn_internet_iptransit": {
        "primary_table": "dbv_ebis_v_amdes_mart_order_iptransit_final",
        "additional_tables": ["dbv_wins_v_amdes_mart_order_iptransit_final"],
        "revenue_column": "prev_revenue",
        "date_column": "periode",
        "service_column": "produk",
        "lis_column": None,
        "bandwidth_column": None,
        "period_type": "integer_yyyymm",
        "schema": "satgas_ai"
    },
    "churn_internet_astinet": {
        "primary_table": "dbv_ebis_v_amdes_mart_order_astinet_final",
        "additional_tables": ["dbv_wins_v_amdes_mart_order_astinet_final"],
        "revenue_column": "prev_revenue",
        "date_column": "periode",
        "service_column": "produk",
        "lis_column": None,
        "bandwidth_column": None,
        "period_type": "integer_yyyymm",
        "schema": "satgas_ai"
    }
}
def get_product_config(product_name: str) -> dict:
    """Get configuration for a specific product"""
    return PRODUCT_CONFIG.get(product_name.lower(), {})

def get_all_product_names() -> list:
    """Get list of all available product names"""
    return list(PRODUCT_CONFIG.keys())

def get_revenue_columns() -> list:
    """Get list of all revenue columns used across products"""
    revenue_cols = set()
    for config in PRODUCT_CONFIG.values():
        if config.get('revenue_column'):
            revenue_cols.add(config['revenue_column'])
    return list(revenue_cols)

def get_date_columns() -> list:
    """Get list of all date columns used across products"""
    date_cols = set()
    for config in PRODUCT_CONFIG.values():
        if config.get('date_column'):
            date_cols.add(config['date_column'])
    return list(date_cols)

def get_service_columns() -> list:
    """Get list of all service columns used across products"""
    service_cols = set()
    for config in PRODUCT_CONFIG.values():
        if config.get('service_column'):
            service_cols.add(config['service_column'])
    return list(service_cols) 
