from pydantic import BaseModel
from typing import Any, Optional, Literal, Dict, List
from pydantic import validator
from datetime import datetime


class NoContentResponse(BaseModel):
    meta: Optional[Any] = ""
    data: Optional[Any] = ""
    status: str = "success"
    code: int = 204
    message: str = ""


class UnauthorizedResponse(BaseModel):
    meta: Optional[Any] = ""
    data: Optional[Any] = ""
    status: str = "error"
    code: int = 401
    message: str = "Unauthorized"


class BadRequestResponse(BaseModel):
    meta: Optional[Any] = ""
    data: Optional[Any] = ""
    status: str = "error"
    code: int = 400
    message: str = "Bad Request"


class ForbiddenResponse(BaseModel):
    meta: Optional[Any] = ""
    data: Optional[Any] = ""
    status: str = "error"
    code: int = 403
    message: str = "You don't have permissions to perform this action"


class NotFoundResponse(BaseModel):
    meta: Optional[Any] = ""
    data: Optional[Any] = ""
    status: str = "error"
    code: int = 404
    message: str = "Not found"


class InternalServerErrorResponse(BaseModel):
    meta: Optional[Any] = ""
    data: Optional[Any] = ""
    status: str = "error"
    code: int = 500
    message: str = "Internal Error"


class NotImplementedResponse(BaseModel):
    meta: Optional[Any] = ""
    data: Optional[Any] = ""
    status: str = "error"
    code: int = 501
    message: str = "Not Yet implemented"


class CudResponseSchema(BaseModel):
    meta: Optional[Any] = ""
    data: Optional[dict] = {"message": ""}
    status: str = "success"
    code: int = 201
    message: str = ""


class SummaryResponse(BaseModel):
    """
    Schema untuk response insight
    """
    meta: Optional[Any] = ""
    data: Dict[str, Any] = {
        "summary_text": str,
        "insight": str,
        "created_at": Optional[datetime]
    }
    status: str = "success"
    code: int = 200
    message: str = "Data berhasil diambil"


class SummaryInsightItem(BaseModel):
    """
    Schema untuk item insight individual
    """
    summary_text: str
    insight: str
    product_type: str
    service_type: Optional[str] = None
    month: str
    year: str
    created_at: Optional[datetime] = None


class SummaryInsightResponse(BaseModel):
    """
    Schema untuk response insight yang dipisah berdasarkan product_type, month, dan year
    """
    meta: Optional[Any] = ""
    data: List[SummaryInsightItem]
    status: str = "success"
    code: int = 200
    message: str = "Data berhasil diambil"


class InsightRequest(BaseModel):
    """
    Schema untuk request insight yang mendukung berbagai jenis analisis
    """
    # Parameter dasar
    product_type: str
    target: str   
    
    # Parameter untuk analisis keyword dan keyword_devisi
    year: Optional[int] = None
    month: Optional[int] = None
    service_type: Optional[str] = None   
    division: Optional[str] = None  
    
    # Parameter untuk analisis compare (format: MM-YYYY)
    compare_from: Optional[str] = None  # Format: MM-YYYY
    compare_to: Optional[str] = None    # Format: MM-YYYY
    
    # Parameter untuk analisis revenue_between
    start_year: Optional[int] = None
    start_month: Optional[int] = None
    end_year: Optional[int] = None
    end_month: Optional[int] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                # Contoh untuk analisis keyword
                # "product_type": "astinet",
                # "target": "revenue",
                # "year": 2024,
                # "month": 2,
                # "service_type": "Astinet",
                
                # Contoh untuk analisis keyword_devisi
                # "product_type": "astinet",
                # "target": "revenue",
                # "year": 2024,
                # "month": 2,
                # "service_type": "Astinet Lite",
                # "division": "DSS",
                
                # Contoh untuk analisis compare
                # "product_type": "astinet",
                # "target": "revenue",
                # "compare_from": "03-2025",
                # "compare_to": "04-2025",
                
                # Contoh untuk analisis revenue_between
                "product_type": "astinet",
                "target": "revenue",
                "start_year": 2024,
                "start_month": 1,
                "end_year": 2024,
                "end_month": 3,
                "service_type": "Astinet"
            }
        }
        
    @validator('compare_from', 'compare_to')
    def validate_date_format(cls, v):
        if v is not None:
            try:
                # Validasi format MM-YYYY
                month, year = v.split('-')
                if not (1 <= int(month) <= 12):
                    raise ValueError("Bulan harus antara 1-12")
                if not (2000 <= int(year) <= 2100):
                    raise ValueError("Tahun harus antara 2000-2100")
            except ValueError as e:
                raise ValueError(f"Format tanggal tidak valid. Gunakan format MM-YYYY (contoh: 03-2025). Error: {str(e)}")
        return v
        
    @validator('start_month', 'end_month')
    def validate_month(cls, v):
        if v is not None and not (1 <= v <= 12):
            raise ValueError("Bulan harus antara 1-12")
        return v
        
    @validator('start_year', 'end_year')
    def validate_year(cls, v):
        if v is not None and not (2000 <= v <= 2100):
            raise ValueError("Tahun harus antara 2000-2100")
        return v