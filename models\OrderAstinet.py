from sqlalchemy import Column, String, Integer, Float, DateTime, Boolean, select, text, func, and_, or_, Numeric, Index
from core.database import Base, db
import logging

logger = logging.getLogger(__name__)

class OrderAstinet(Base):
    # Menggunakan __table_args__ untuk menentukan schema dan indeks
    __tablename__ = "dbv_ebis_v_amdes_nf_aktif_astinet_finalx"
    __table_args__ = (
        Index('idx_astinet_periode', 'periode'),
        Index('idx_astinet_periode_service', 'periode', 'variant'),
        Index('idx_astinet_divisi', 'divisi'),
        {'schema': 'satgas_ai'}
    )

    __additional_tablename__ = "dbv_wins_v_amdes_nf_aktif_astinet_finalx"

    periode = Column(String, primary_key=True)
    periode_date = Column(DateTime)
    customer_name = Column(String)
    customer_ref = Column(String)
    account_num = Column(String)
    account_name = Column(String)
    business_share = Column(String)
    divisi = Column(String)
    segmen = Column(String)
    currency_code = Column(String)
    product_label = Column(String)
    cust_order_num = Column(String)
    billcomdate = Column(String)
    install = Column(Numeric(38, 20))
    abonemen = Column(Numeric(38, 20))
    circuit_id = Column(String)
    circuit_name = Column(String)
    circuit_speed = Column(String)
    product_name = Column(String)
    product_seq = Column(Numeric(38, 20))
    status_reason_txt = Column(String)
    effective_dtm = Column(DateTime)
    tariff_name = Column(String)
    ppn = Column(String)
    tgl_suspend = Column(DateTime)
    update_dat = Column(DateTime)
    value_speed = Column(Numeric(38, 20))
    variant = Column(String)
    revenue = Column(Numeric(38, 20))
    tarif = Column(Numeric(38, 20))
    hjm = Column(Numeric(38, 20))
    reg = Column(String)
    reg_new = Column(String)
    witel = Column(String)
    witel_new = Column(String)
    zona_tarif = Column(String)
    cust_cndc = Column(String)
    pull_thru_cndc = Column(String)
    order_subtype = Column(String)
    sto_code = Column(String)
    sto = Column(String)
    max_traffic = Column(Numeric(38, 20))
    nip_nas_cbase = Column(String)
    standard_name_cbase = Column(String)
    segmen_cbase = Column(String)
    bill_mny = Column(Numeric(38, 20))
    cx_termin_value = Column(Numeric(38, 20))
    cx_termin_flg = Column(String)
    termin_seq_flg = Column(String)
    cx_otc_amount = Column(Numeric(38, 20))
    termin_hjm = Column(Numeric(38, 20))
    termin_tarif = Column(Numeric(38, 20))
    cntrc_duration = Column(Numeric(38, 20))
    jml_sequence = Column(Numeric(38, 20))
    revenue_final = Column(Numeric(38, 20))
    hjm_final = Column(Numeric(38, 20))
    tarif_final = Column(Numeric(38, 20))
    flag_otc = Column(String)
    otc_seq_flg = Column(String)
    revenue_otc = Column(Numeric(38, 20))
    tarif_otc = Column(Numeric(38, 20))
    hjm_otc = Column(Numeric(38, 20))
    numeric_of_last_mile = Column(Numeric(38, 20))
    tarif_last_mile = Column(Numeric(38, 20))
    additional_ip = Column(Numeric(38, 20))
    tarif_additional_ip = Column(Numeric(38, 20))
    skema = Column(String)
    number_of_last_mile = Column(Numeric(38, 20))

    @classmethod
    async def get_data_with_fallback(cls, session, query, year: int):
        """Scan data dari kedua tabel dan gabungkan hasilnya"""
        try:
            all_data = []
            
            # Scan dari tabel utama (ebis)
            logger.info(f"Scanning data dari {cls.__tablename__}")
            result = await session.execute(query)
            ebis_data = result.all()
            if ebis_data:
                logger.info(f"Ditemukan {len(ebis_data)} data dari {cls.__tablename__}")
                all_data.extend(ebis_data)
            
            # Scan dari tabel tambahan (wins)
            logger.info(f"Scanning data dari {cls.__additional_tablename__}")
            
            # Ambil query string dan parameter dari query asli
            compiled_query = query.compile(compile_kwargs={"literal_binds": True})
            query_str = str(compiled_query)
            params = compiled_query.params
            
            # Ganti nama tabel dalam query string
            additional_query_str = query_str.replace(
                f"{cls.__table_args__[3]['schema']}.{cls.__tablename__}", 
                f"{cls.__table_args__[3]['schema']}.{cls.__additional_tablename__}"
            )
            
            # Buat query baru menggunakan text()
            additional_query = text(additional_query_str)
            
            # Jalankan query dengan parameter yang sama
            result = await session.execute(additional_query, params)
            wins_data = result.all()
            if wins_data:
                logger.info(f"Ditemukan {len(wins_data)} data dari {cls.__additional_tablename__}")
                all_data.extend(wins_data)
            
            # Log total data yang ditemukan
            total_data = len(all_data)
            if total_data > 0:
                logger.info(f"Total data yang ditemukan dari kedua tabel: {total_data}")
            else:
                logger.warning("Tidak ada data yang ditemukan dari kedua tabel")
            
            return all_data
            
        except Exception as e:
            logger.error(f"Error dalam get_data_with_fallback: {str(e)}", exc_info=True)
            raise
