from sqlalchemy import Column, String, Integer, Float, DateTime, Boolean, select, text, func, and_, Numeric, Index
from core.database import Base, db
import logging

logger = logging.getLogger(__name__)

class OrderVoicePots(Base):
    __tablename__ = "dbv_ebis_v_osr_pots_hsi_summary"
    __table_args__ = (
        Index('idx_voice_pots_periode_billing', 'periode_billing'),
        Index('idx_voice_pots_produk', 'produk'),
        {'schema': 'satgas_ai'}
    )

    __additional_tablename__ = None  # Tidak ada tabel tambahan

    periode_billing = Column(DateTime, primary_key=True)
    periode = Column(String)
    produk = Column(String)
    bundling = Column(String)
    kw = Column(String)
    usia_kw = Column(String)
    ket_ct0 = Column(String)
    packet = Column(String)
    ubis = Column(String)
    subsegmen = Column(String)
    divisi = Column(String)
    witel = Column(String)
    datel = Column(String)
    jumlah_lis = Column(String)
    revenue = Column(Numeric(38, 20))
    datms = Column(String)
    abrv_art = Column(String)
    paket = Column(String)
    mbps = Column(String)
    varian = Column(String)
    service_subgroup = Column(String)
    summary_grouping = Column(String)
    hjm = Column(String)
    hjm_all = Column(String)
    created_at = Column(DateTime)
    updated_at = Column(DateTime)

    @classmethod
    async def get_data_with_fallback(cls, session, query, year: int):
        """Scan data dari tabel utama saja karena tidak ada fallback"""
        try:
            logger.info(f"Scanning data dari {cls.__tablename__}")
            result = await session.execute(query)
            data = result.all()
            if data:
                logger.info(f"Ditemukan {len(data)} data dari {cls.__tablename__}")
            else:
                logger.warning("Tidak ada data yang ditemukan")
            return data
            
        except Exception as e:
            logger.error(f"Error dalam get_data_with_fallback: {str(e)}", exc_info=True)
            raise 