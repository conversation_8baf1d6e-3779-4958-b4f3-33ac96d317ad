import pandas as pd
import logging
 
logger = logging.getLogger(__name__)
 
def generate_monthly_summary_schedule(data_configs, target_column):
    results = []
 
 
    # Determine target type from target_column
    target_type = None
    if target_column in ["revenue", "real_revenue", "revenue_final", "abonemen", "rupiah", "trems_rev", "prev_revenue"]:
        target_type = "revenue"
    elif target_column in ["lis", "jumlah_lis", "product_label", "produk_label", "periode", "notel", "sid"]:
        target_type = "lis"
    elif target_column in ["bandwidth", "value_speed_mbps", "value_speed", "value_bw_mbps", "value_speed_gbps", "sdwan_bandwidth"]:
        target_type = "bandwidth"
    else:
        target_type = "revenue"  # Default to revenue if unknown
    
    logger.info(f"Processing target column: {target_column} as type: {target_type}")
 
    for config in data_configs:
        df = config['df'].copy()
        service_type = config['service_type']
        date_column = config.get('date_column')
 
        # cek kolom yang sesuai target
        if target_column not in df.columns:
            logger.warning(f"Kolom untuk target '{target_column}' tidak ditemukan pada service {service_type}, dilewati.")
            continue
 
        # pastikan date_column datetime dengan format yang benar
        try:
            # Coba format YYYYMM terlebih dahulu
            if df[date_column].dtype in ['int64', 'float64'] or all(df[date_column].astype(str).str.len() == 6):
                # Format YYYYMM (202505)
                df[date_column] = pd.to_datetime(df[date_column].astype(str), format='%Y%m', errors='coerce')
            else:
                # Format lainnya
                df[date_column] = pd.to_datetime(df[date_column], errors='coerce')
            
            # Validasi hasil parsing
            if df[date_column].isna().all():
                logger.warning(f"Tidak ada tanggal valid setelah parsing untuk service {service_type}")
                continue
                
        except Exception as e:
            logger.warning(f"Error converting date column {date_column}: {e}")
            continue
 
        # cari periode terakhir (current) → bulan & tahun
        last_date = df[date_column].max()
        if pd.isna(last_date):
            logger.warning(f"Tidak ada tanggal valid untuk service {service_type}")
            continue
            
        curr_year, curr_month = last_date.year, last_date.month
 
        # periode sebelumnya
        prev_date = (last_date - pd.DateOffset(months=1))
        prev_year, prev_month = prev_date.year, prev_date.month
 
        # filter data
        curr_df = df[(df[date_column].dt.year == curr_year) & (df[date_column].dt.month == curr_month)]
        prev_df = df[(df[date_column].dt.year == prev_year) & (df[date_column].dt.month == prev_month)]

        logger.info(f"Analisis untuk {service_type} ({target_column}): Membandingkan {curr_month}-{curr_year} dengan {prev_month}-{prev_year}")
        logger.info(f"Rows → prev={len(prev_df)}, curr={len(curr_df)}")
 
        if curr_df.empty or prev_df.empty:
            logger.warning(f"Data kosong untuk perbandingan {target_column} pada layanan {service_type}.")
            continue
 
        # Konversi ke float untuk menghindari error float + Decimal
        try:
            curr_total = float(pd.to_numeric(curr_df[target_column], errors='coerce').sum())
            prev_total = float(pd.to_numeric(prev_df[target_column], errors='coerce').sum())
        except (TypeError, ValueError) as e:
            logger.warning(f"Error konversi tipe data untuk {target_column} pada {service_type}: {e}")
            continue
 
        logger.info(f"Totals → prev_total={prev_total:,.2f}, curr_total={curr_total:,.2f}")

        if prev_total == 0:
            logger.warning(f"Total {target_column} pada periode sebelumnya untuk {service_type} adalah nol, analisis dilewati.")
            continue
 
        diff = curr_total - prev_total
        pct = (diff / prev_total) * 100 if prev_total else 0
        logger.info(f"Diff/Pct → diff={diff:,.2f}, pct={pct:.2f}%")
        arah = "peningkatan" if diff > 0 else "penurunan" if diff < 0 else "stabil"
 
        # format angka berdasarkan target type
        if target_type == "revenue":
            nilai_format = f"Rp{abs(diff):,.0f}".replace(",", ".")
        elif target_type == "bandwidth":
            if "gbps" in target_column.lower():
                nilai_format = f"{abs(diff):,.2f} Gbps"
            else:
                nilai_format = f"{abs(diff):,.2f} Mbps"
        else:
            nilai_format = f"{abs(diff):,.0f}"
 
        bulan_str = pd.to_datetime(f"{curr_year}-{curr_month:02}-01").strftime('%B %Y')
 
        msg = (
            f"Layanan {service_type} mengalami {arah} {target_type} sebesar {abs(pct):.2f}% pada bulan {bulan_str}. "
            f"Total perubahan {target_type} adalah {nilai_format}. "
            "Tambahkan insight detail sesuai dokumen."
        )
 
        results.append({
            "service_type": service_type,
            "target": target_type,
            "summary_text": msg
        })
 
    return results