import base64
import io
import matplotlib.pyplot as plt
import pandas as pd
import re
import seaborn as sns
from datetime import datetime
 
# Filter columns untuk setiap model
nf_aktif_astinet = ['account_name', 'divisi', 'abonemen', 'product_name', 'tariff_name', 'service_type_1', 'bandwidth', 'zona_tarif', 'notelp', 'revenue_final', 'diskon_percent', 'sto', 'variant', 'hjm']
sap = ['gl_dss_prod_grp', 'gl_dss_prod_cat', 'gl_desc', 'divisi', 'periode_rev', 'real_revenue', 'target_revenue']

def plot_trend_with_feature_contributions(result, selected_features):
    # Data tren
    revenue_values = [result['revenue_last'], result['revenue_current']]
    months = ['Bulan Lalu', 'Bulan Sekarang']
 
    # Deteksi fitur yang berubah
    changed_feats = []
    for desc in result["long_summary"].split("penyebab")[1].split("Ranking")[0].split(", dan "):
        for feat in selected_features:
            if f"*{feat}*" in desc:
                changed_feats.append(feat)
 
    feat_counts = {feat: 1 if feat in changed_feats else 0 for feat in selected_features}
    total_change = sum(feat_counts.values())
    feat_percent = {feat: (v / total_change * 100 if total_change else 0) for feat, v in feat_counts.items()}
 
    # Plot
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10,6), gridspec_kw={'height_ratios': [2, 1]})
    fig.suptitle('Trend Revenue dan Kontribusi Fitur')
 
    # Line chart
    ax1.plot(months, revenue_values, marker='o', color='blue', linewidth=2)
    ax1.set_ylabel('Real Revenue')
    ax1.set_title(f"Trend: {result['trend'].capitalize()} ({result['delta']:+,.0f})", fontsize=12)
    ax1.grid(True)
 
    # Bar chart
    ax2.bar(feat_percent.keys(), feat_percent.values(), color='orange')
    ax2.set_ylabel('% Pengaruh')
    ax2.set_xticklabels(feat_percent.keys(), rotation=45, ha='right')
    ax2.set_ylim(0, 100)
 
    plt.tight_layout()
    plt.subplots_adjust(top=0.88)
 
    buf = io.BytesIO()

    fig.savefig(buf, format='png')

    buf.seek(0)

    img_base64 = base64.b64encode(buf.read()).decode('utf-8')

    buf.close()
 
    return img_base64
 
def plot_revenue_to_base64(result_text, title="Tren Revenue Bulanan"):
    # 1. Parse data
    lines = result_text.strip().split("\n")[1:]
    data = []
    for line in lines:
        match = re.match(r"Revenue (\w{3}) (\d{4}): ([\d\.\-]+)M", line)
        if match:
            month_str, year, revenue = match.groups()
            month = pd.to_datetime(month_str, format="%b").month
            data.append({
                "year": int(year),
                "month": month,
                "revenue": float(revenue)
            })
 
    # 2. Konversi ke DataFrame
    df = pd.DataFrame(data)
    df["periode"] = pd.to_datetime(df["year"].astype(str) + "-" + df["month"].astype(str).str.zfill(2))
    df = df.sort_values("periode")
 
    # 3. Plot ke gambar
    fig, ax = plt.subplots(figsize=(12, 6))
    ax.plot(df["periode"], df["revenue"], marker='o', color='teal', linewidth=2)
    ax.set_title(title, fontsize=14)
    ax.set_xlabel("Periode")
    ax.set_ylabel("Revenue (Miliar)")
    ax.grid(True)
    plt.xticks(rotation=45)
    plt.tight_layout()
 
    # 4. Simpan ke buffer dan encode ke base64
    buffer = io.BytesIO()
    plt.savefig(buffer, format='png')
    plt.close(fig)
    buffer.seek(0)
    img_base64 = base64.b64encode(buffer.read()).decode('utf-8')
    return img_base64

def plot_compare_chart(compare_result, total_prev, total_curr):
    labels = [compare_result["compare_from"], compare_result["compare_to"]]
    values = [total_prev, total_curr]
 
    fig, ax = plt.subplots(figsize=(8, 5))
    bars = ax.bar(labels, values, color=['skyblue', 'salmon'])
 
    # Tambahkan label nilai
    for bar in bars:
        height = bar.get_height()
        ax.annotate(f'Rp {int(height):,}'.replace(",", "."),
                    xy=(bar.get_x() + bar.get_width() / 2, height),
                    xytext=(0, 5),
                    textcoords="offset points",
                    ha='center', va='bottom')
 
    ax.set_title(f'Perbandingan {compare_result["target"]}')
    ax.set_ylabel("Total")
    ax.grid(axis='y', linestyle='--', alpha=0.7)
    plt.tight_layout()
 
    # Simpan ke buffer sebagai base64
    buf = io.BytesIO()
    fig.savefig(buf, format='png')
    buf.seek(0)
    img_base64 = base64.b64encode(buf.read()).decode('utf-8')
    buf.close()
 
    return img_base64

def LIS_chart(df, column_lis, top_n=10):
    if column_lis not in df.columns:
        raise ValueError(f"Kolom '{column_lis}' tidak ditemukan di DataFrame.")
    
    distinct_count = df[column_lis].nunique()
    top_values = df[column_lis].value_counts().nlargest(top_n)

    # Plot
    plt.figure(figsize=(10, 6))
    ax = sns.barplot(x=top_values.values, y=top_values.index, palette='Blues_r')
    plt.title(f"Top {top_n} '{column_lis}' berdasarkan Frekuensi")
    plt.xlabel("Jumlah")
    plt.ylabel(column_lis)

    # Tambahkan keterangan jumlah distinct di bawah chart
    plt.figtext(0.5, -0.05, 
                f"Jumlah total nilai unik di '{column_lis}': {distinct_count:,}", 
                wrap=True, horizontalalignment='center', fontsize=10)

    plt.tight_layout()
    
    # Simpan ke base64
    buffer = io.BytesIO()
    plt.savefig(buffer, format='png', bbox_inches="tight")
    plt.close()
    buffer.seek(0)
    base64_chart = base64.b64encode(buffer.read()).decode('utf-8')

    return {
        'distinct_count': distinct_count,
        'base64_chart': base64_chart
    }
    
def ach_chart(df, column_date, tahun, bulan, 
              divisi_column=None, divisi=None, 
              service_column=None, service_type=None):
    # Validasi kolom
    for col in ['real_revenue', 'target_revenue', column_date]:
        if col not in df.columns:
            raise ValueError(f"Kolom '{col}' tidak ditemukan di DataFrame.")
    
    df = df.copy()
    df[column_date] = pd.to_datetime(df[column_date], errors='coerce')
    df = df[(df[column_date].dt.year == int(tahun)) & (df[column_date].dt.month == int(bulan))]

    # Filter berdasarkan input
    if divisi:
        if not divisi_column or divisi_column not in df.columns:
            raise ValueError("Kolom divisi tidak valid atau tidak diberikan.")
        df = df[df[divisi_column] == divisi]
        
    if service_type:
        if not service_column or service_column not in df.columns:
            raise ValueError("Kolom service tidak valid atau tidak diberikan.")
        df = df[df[service_column] == service_type]
    
    # Tentukan group_col dan judul chart
    if divisi and not service_type:
        group_col = service_column
        title = f"Achievement Divisi {divisi}"
    elif service_type and not divisi:
        group_col = divisi_column
        title = f"Achievement {service_type}"
    elif divisi and service_type:
        group_col = None  # hanya satu bar
        title = f"Achievement {divisi} - {service_type}"
    else:
        group_col = divisi_column
        title = "Achievement Global (Keseluruhan)"

    # Agregasi data
    if group_col:
        grouped = df.groupby(group_col).agg({
            'real_revenue': 'sum',
            'target_revenue': 'sum'
        }).reset_index()
        grouped['achievement'] = (grouped['real_revenue'] - grouped['target_revenue']) / grouped['target_revenue']
        grouped['label'] = grouped[group_col].astype(str)
    else:
        total_real = df['real_revenue'].sum()
        total_target = df['target_revenue'].sum()
        achv = (total_real - total_target) / total_target if total_target != 0 else 0
        grouped = pd.DataFrame({
            'label': [f"{divisi} - {service_type}"],
            'real_revenue': [total_real],
            'target_revenue': [total_target],
            'achievement': [achv]
        })

    # Plot
    plt.figure(figsize=(10, 6))
    bar_colors = ['lightgreen' if r > t else 'salmon' for r, t in zip(grouped['real_revenue'], grouped['target_revenue'])]

    plt.bar(grouped['label'], grouped['real_revenue'], color=bar_colors, label='Real Revenue')
    plt.bar(grouped['label'], grouped['target_revenue'], color='khaki', alpha=0.6, label='Target Revenue')

    plt.xticks(rotation=30, ha='right')
    plt.title(title)
    plt.ylabel("Revenue")
    plt.legend()

    # Tambahkan nilai achievement di atas bar
    for i, (label, real, target, achv) in enumerate(zip(grouped['label'], grouped['real_revenue'], grouped['target_revenue'], grouped['achievement'])):
        plt.text(i, max(real, target) * 1.02, f"{achv:.2%}", ha='center', fontsize=9)

    # Tambahkan keterangan summary global jika tidak difilter spesifik
    if not divisi and not service_type:
        total_real = grouped['real_revenue'].sum()
        total_target = grouped['target_revenue'].sum()
        total_ach = (total_real - total_target) / total_target if total_target != 0 else 0
        plt.figtext(0.5, -0.05, f"Total Achievement Global: {total_ach:.2%}", ha='center', fontsize=9)

    plt.tight_layout()

    # Simpan ke base64
    buffer = io.BytesIO()
    plt.savefig(buffer, format='png', bbox_inches='tight')
    plt.close()
    buffer.seek(0)
    base64_chart = base64.b64encode(buffer.read()).decode('utf-8')

    return base64_chart

def growth_yoy_chart(df, column_date, tahun=None):
    if column_date not in df.columns:
        raise ValueError(f"Kolom '{column_date}' tidak ditemukan.")

    df = df.copy()
    df[column_date] = pd.to_datetime(df[column_date], errors='coerce')
    df = df.dropna(subset=[column_date, 'real_revenue'])

    if tahun is None:
        tahun = datetime.now().year
    else:
        tahun = int(tahun)

    bulan_ini = datetime.now().month

    # Ambil data bulan yg sama dari tahun saat ini dan tahun lalu
    df['tahun'] = df[column_date].dt.year
    df['bulan'] = df[column_date].dt.month

    current_month = df[(df['tahun'] == tahun) & (df['bulan'] == bulan_ini)]
    last_year_month = df[(df['tahun'] == tahun - 1) & (df['bulan'] == bulan_ini)]

    real_this = current_month['real_revenue'].sum()
    real_last = last_year_month['real_revenue'].sum()

    if real_last == 0:
        growth = 0
    else:
        growth = (real_this - real_last) / real_last

    # Data untuk plot
    data = pd.DataFrame({
        'Tahun': [tahun - 1, tahun],
        'Real Revenue': [real_last, real_this]
    })
    
    warna = ['salmon' if i == tahun - 1 else 'lightgreen' for i in data['Tahun']]

    # Plot
    plt.figure(figsize=(6, 5))
    sns.barplot(x='Tahun', y='Real Revenue', data=data, palette=warna)

    plt.title(f"Pertumbuhan YoY Bulan {bulan_ini:02d}")
    plt.ylabel("Real Revenue")
    for idx, val in enumerate(data['Real Revenue']):
        plt.text(idx, val * 1.01, f"{val:,.0f}", ha='center')

    # Keterangan growth
    plt.figtext(0.5, -0.1, f"Growth YoY: {growth:.2%}", ha='center', fontsize=10)

    plt.tight_layout()

    # Simpan ke base64
    buffer = io.BytesIO()
    plt.savefig(buffer, format='png', bbox_inches='tight')
    plt.close()
    buffer.seek(0)
    base64_img = base64.b64encode(buffer.read()).decode('utf-8')

    return base64_img

def growth_mom_chart(df, column_date, tahun=None, bulan=None):
    if column_date not in df.columns:
        raise ValueError(f"Kolom '{column_date}' tidak ditemukan.")

    df = df.copy()
    df[column_date] = pd.to_datetime(df[column_date], errors='coerce')
    df = df.dropna(subset=[column_date, 'real_revenue'])

    now = datetime.now()
    tahun = int(tahun) if tahun else now.year
    bulan = int(bulan) if bulan else now.month

    # Hitung bulan sebelumnya
    if bulan == 1:
        prev_bulan = 12
        prev_tahun = tahun - 1
    else:
        prev_bulan = bulan - 1
        prev_tahun = tahun

    df['tahun'] = df[column_date].dt.year
    df['bulan'] = df[column_date].dt.month

    this_month = df[(df['tahun'] == tahun) & (df['bulan'] == bulan)]
    last_month = df[(df['tahun'] == prev_tahun) & (df['bulan'] == prev_bulan)]

    real_this = this_month['real_revenue'].sum()
    real_last = last_month['real_revenue'].sum()

    if real_last == 0:
        growth = 0
    else:
        growth = (real_this - real_last) / real_last

    # Data plot
    data = pd.DataFrame({
        'Bulan': [f"{prev_tahun}-{prev_bulan:02d}", f"{tahun}-{bulan:02d}"],
        'Real Revenue': [real_last, real_this]
    })

    warna = ['salmon' if i == 0 else 'lightgreen' for i in range(len(data))]

    # Plot
    plt.figure(figsize=(6, 5))
    sns.barplot(x='Bulan', y='Real Revenue', data=data, palette=warna)

    plt.title(f"Pertumbuhan MoM {tahun}-{bulan:02d}")
    plt.ylabel("Real Revenue")
    for idx, val in enumerate(data['Real Revenue']):
        plt.text(idx, val * 1.01, f"{val:,.0f}", ha='center')

    # Keterangan growth
    plt.figtext(0.5, -0.1, f"Growth MoM: {growth:.2%}", ha='center', fontsize=10)

    plt.tight_layout()

    # Simpan ke base64
    buffer = io.BytesIO()
    plt.savefig(buffer, format='png', bbox_inches='tight')
    plt.close()
    buffer.seek(0)
    base64_img = base64.b64encode(buffer.read()).decode('utf-8')

    return base64_img

def data_product(df, service_column: str, service_type: str = None, filter_columns: list = None):
    # Mapping alias untuk kolom
    column_aliases = {
        'gl_dss_prod_grp': 'Group',
        'gl_dss_prod_cat': 'Kategori',
        'gl_desc': 'Deskripsi Produk',
        'divisi': 'Divisi',
        'periode_rev': 'Periode',
        'real_revenue': 'Revenue',
        'target_revenue': 'Target',
        'abonemen': 'Abonemen',
        'account_name': 'Nama Account',
        'product_name': 'Nama Produk',
        'tariff_name': 'Nama Tarif',
        'service_type_1': 'Service Type1',
        'bandwidth': 'Bandwidth',
        'zona_tarif': 'Zona Tarif',
        'notelp': 'No Telp',
        'revenue_final': 'Revenue',
        'diskon_percent': 'Diskon Percent',
        'sto': 'STO',
        'variant': 'Variant',
        'hjm': 'HJM'
    }
    
    # Validasi input
    if service_column not in df.columns:
        raise ValueError(f"Kolom '{service_column}' tidak ditemukan dalam DataFrame.")
    
    if not filter_columns:
        raise ValueError("Parameter 'filter_columns' harus diisi.")
    
    for col in filter_columns:
        if col not in df.columns:
            raise ValueError(f"Kolom '{col}' tidak ditemukan dalam DataFrame.")
    
    # Buat filter boolean untuk menghindari copy DataFrame besar
    # Filter 1: Hapus baris dengan nilai null pada service_column
    mask_not_null = df[service_column].notna()
    
    # Filter 2: Filter berdasarkan service_type jika ada
    if service_type:
        mask_service = df[service_column] == service_type
        final_mask = mask_not_null & mask_service
        filter_info = f"Service: {service_type}"
    else:
        final_mask = mask_not_null
        filter_info = f"Semua data dari kolom: {service_column}"
    
    # Hitung total baris setelah filtering tanpa membuat copy
    total_filtered_rows = final_mask.sum()
    
    # Cek apakah ada data setelah filtering
    if total_filtered_rows == 0:
        raise ValueError("Tidak ada data yang sesuai dengan filter.")
    
    # Ambil maksimal 20 baris untuk ditampilkan langsung dengan filter
    df_show = df.loc[final_mask, filter_columns].head(20)
    
    # Buat label kolom dengan alias
    col_labels = [column_aliases.get(col, col) for col in df_show.columns]
    
    # Hitung lebar kolom berdasarkan panjang konten
    col_widths = []
    for col in df_show.columns:
        # Hitung panjang maksimum antara header dan konten
        header_len = len(column_aliases.get(col, col))
        content_len = df_show[col].astype(str).apply(len).max() if not df_show[col].empty else 0
        max_len = max(header_len, content_len)
        col_widths.append(max(max_len * 0.08, 1.5))  # Minimum width 1.5
    
    # Tentukan ukuran figure
    fig_width = max(sum(col_widths) + 2, 8)  # Minimum width 8
    fig_height = max(len(df_show) * 0.4 + 2, 4)  # Minimum height 4
    
    # Buat figure dan axis
    fig, ax = plt.subplots(figsize=(fig_width, fig_height))
    ax.axis('off')
    
    # Judul dengan informasi filter
    title_text = f"Data Product - {filter_info}\nTotal data: {total_filtered_rows} baris"
    if len(df_show) < total_filtered_rows:
        title_text += f" (Menampilkan {len(df_show)} baris pertama)"
    
    ax.text(0.5, 0.95, title_text, transform=ax.transAxes,
            fontsize=12, va='top', ha='center', weight='bold')
    
    # Buat tabel
    table = ax.table(cellText=df_show.values,
                     colLabels=col_labels,
                     colWidths=col_widths,
                     cellLoc='center',
                     loc='center',
                     bbox=[0, 0, 1, 0.8])  # Posisi tabel
    
    # Styling tabel
    table.auto_set_font_size(False)
    table.set_fontsize(9)
    table.scale(1.2, 1.5)
    
    # Style untuk header
    for (row, col), cell in table.get_celld().items():
        if row == 0:  # Header row
            cell.set_fontsize(10)
            cell.set_text_props(weight='bold')
            cell.set_facecolor('#4CAF50')
            cell.set_text_props(color='white')
        else:  # Data rows
            if row % 2 == 0:
                cell.set_facecolor('#f9f9f9')
            else:
                cell.set_facecolor('#ffffff')
    
    # Tambahkan informasi di bawah tabel
    # info_text = f"Kolom yang ditampilkan: {', '.join(col_labels)}"
    # ax.text(0.5, 0.05, info_text, transform=ax.transAxes,
    #         fontsize=10, va='bottom', ha='center', style='italic')
    
    # Simpan ke buffer
    buffer = io.BytesIO()
    plt.savefig(buffer, format='png', bbox_inches='tight', dpi=300)
    plt.close()
    
    # Convert ke base64
    buffer.seek(0)
    base64_img = base64.b64encode(buffer.read()).decode('utf-8')
    
    return base64_img