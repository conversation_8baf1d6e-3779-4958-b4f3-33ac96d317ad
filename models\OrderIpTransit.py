# from sqlalchemy import Column, String, Integer, Float, DateTime, Boolean, select, text, func, and_, Numeric, Index
# from core.database import Base, db
# import logging

# logger = logging.getLogger(__name__)

# class OrderIpTransit(Base):
#     __tablename__ = "dbv_ebis_v_amdes_nf_aktif_iptransit_final"
#     __table_args__ = (
#         Index('idx_iptransit_periode', 'periode'),
#         Index('idx_iptransit_service_type', 'service_type'),
#         Index('idx_iptransit_divisi', 'divisi'),
#         {'schema': 'satgas_ai'}
#     )

#     __additional_tablename__ = "dbv_wins_v_amdes_nf_aktif_iptransit_final"


#     revenue_otc = Column(Float, primary_key=True)
#     tarif_otc = Column(Float)
#     hjm_otc = Column(Float)
#     term_payment = Column(String)
#     revenue_mrc = Column(Float)
#     tarif_mrc = Column(Float)
#     hjm_mrc = Column(Float)
#     mrc_seq_flg = Column(Boolean)
#     flag_top = Column(Boolean)
#     li_status_date = Column(DateTime)
#     jenis_akun = Column(String)
#     end_user_telin = Column(String)
#     number_of_last_mile = Column(Integer)
#     tarif_last_mile = Column(Float)
#     reg_new = Column(String)
#     witel_new = Column(String)
#     variant_burstable = Column(String)
#     flag_burstable = Column(Boolean)
#     bandwidth_burstable = Column(Float)
#     burstable_bw_iptx_domestik = Column(Float)
#     burstable_bw_iptx_global = Column(Float)

#     @classmethod
#     async def get_data_with_fallback(cls, session, query, year: int):
#         """Scan data dari kedua tabel dan gabungkan hasilnya"""
#         try:
#             all_data = []
            
#             # Scan dari tabel utama (ebis)
#             logger.info(f"Scanning data dari {cls.__tablename__}")
#             result = await session.execute(query)
#             ebis_data = result.all()
#             if ebis_data:
#                 logger.info(f"Ditemukan {len(ebis_data)} data dari {cls.__tablename__}")
#                 all_data.extend(ebis_data)
            
#             # Scan dari tabel tambahan (wins)
#             logger.info(f"Scanning data dari {cls.__additional_tablename__}")
            
#             # Ambil query string dan parameter dari query asli
#             compiled_query = query.compile(compile_kwargs={"literal_binds": True})
#             query_str = str(compiled_query)
#             params = compiled_query.params
            
#             # Ganti nama tabel dalam query string
#             additional_query_str = query_str.replace(
#                 f"{cls.__table_args__[3]['schema']}.{cls.__tablename__}", 
#                 f"{cls.__table_args__[3]['schema']}.{cls.__additional_tablename__}"
#             )
            
#             # Buat query baru menggunakan text()
#             additional_query = text(additional_query_str)
            
#             # Jalankan query dengan parameter yang sama
#             result = await session.execute(additional_query, params)
#             wins_data = result.all()
#             if wins_data:
#                 logger.info(f"Ditemukan {len(wins_data)} data dari {cls.__additional_tablename__}")
#                 all_data.extend(wins_data)
            
#             # Log total data yang ditemukan
#             total_data = len(all_data)
#             if total_data > 0:
#                 logger.info(f"Total data yang ditemukan dari kedua tabel: {total_data}")
#             else:
#                 logger.warning("Tidak ada data yang ditemukan dari kedua tabel")
            
#             return all_data
            
#         except Exception as e:
#             logger.error(f"Error dalam get_data_with_fallback: {str(e)}", exc_info=True)
#             raise
