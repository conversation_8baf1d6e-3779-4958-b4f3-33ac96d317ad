from sqlalchemy import Column, String, Integer, Float, DateTime, Boolean, select, text, func, and_, Numeric, Index
from core.database import Base, db
import logging

logger = logging.getLogger(__name__)

class OrderChurnInternetIpTransit(Base):
    __tablename__ = "dbv_ebis_v_amdes_mart_order_iptransit_final"
    __table_args__ = (
        Index('idx_churn_internet_iptransit_periode', 'periode'),
        {'schema': 'satgas_ai'}
    )

    __additional_tablename__ = "dbv_wins_v_amdes_mart_order_iptransit_final"

    periode = Column(String, primary_key=True)
    orderid = Column(String)
    customerid = Column(String)
    nip_nas = Column(String)
    ordertype = Column(String)
    name1sold = Column(String)
    divisi = Column(String)
    segmen = Column(String)
    sid = Column(String)
    li_statusdate = Column(DateTime)
    li_statusdate_1 = Column(DateTime)
    service_witel = Column(String)
    sto = Column(String)
    value_speed_mbps = Column(Float)
    qos_normalized = Column(Float)
    service_type = Column(String)
    witel = Column(String)
    zona_tarif_new = Column(String)
    tarif_total = Column(Float)
    hjm = Column(Float)
    treg = Column(String)
    service_region = Column(String)
    custaccntname = Column(String)
    bw_tenoss = Column(String)
    service_witel_fix = Column(String)
    prevorder = Column(String)
    prev_tarif_total = Column(Float)
    prev_hjm = Column(Float)
    prev_value_speed_mbps = Column(Float)
    prev_revenue = Column(String)  
    revenue = Column(String)  
    prev_order_type = Column(String)
    periode_tibs = Column(Float)
    migrasi = Column(Boolean)
    reason_do = Column(String)
    order_created_date = Column(DateTime)
    actual_date = Column(DateTime)
    account_name = Column(String)
    created_at = Column(DateTime)
    updated_at = Column(DateTime)

    @classmethod
    async def get_data_with_fallback(cls, session, query, year: int):
        """Scan data dari kedua tabel dan gabungkan hasilnya"""
        try:
            all_data = []
            
            # Scan dari tabel utama (ebis)
            logger.info(f"Scanning data dari {cls.__tablename__}")
            result = await session.execute(query)
            ebis_data = result.all()
            if ebis_data:
                logger.info(f"Ditemukan {len(ebis_data)} data dari {cls.__tablename__}")
                all_data.extend(ebis_data)
            
            # Scan dari tabel tambahan (wins)
            logger.info(f"Scanning data dari {cls.__additional_tablename__}")
            
            # Ambil query string dan parameter dari query asli
            compiled_query = query.compile(compile_kwargs={"literal_binds": True})
            query_str = str(compiled_query)
            params = compiled_query.params
            
            # Ganti nama tabel dalam query string
            additional_query_str = query_str.replace(
                f"{cls.__table_args__[1]['schema']}.{cls.__tablename__}", 
                f"{cls.__table_args__[1]['schema']}.{cls.__additional_tablename__}"
            )
            
            # Buat query baru menggunakan text()
            additional_query = text(additional_query_str)
            
            # Jalankan query dengan parameter yang sama
            result = await session.execute(additional_query, params)
            wins_data = result.all()
            if wins_data:
                logger.info(f"Ditemukan {len(wins_data)} data dari {cls.__additional_tablename__}")
                all_data.extend(wins_data)
            
            # Log total data yang ditemukan
            total_data = len(all_data)
            if total_data > 0:
                logger.info(f"Total data yang ditemukan dari kedua tabel: {total_data}")
            else:
                logger.warning("Tidak ada data yang ditemukan dari kedua tabel")
            
            return all_data
            
        except Exception as e:
            logger.error(f"Error dalam get_data_with_fallback: {str(e)}", exc_info=True)
            raise 